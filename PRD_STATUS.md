# Floor Plan Analyzer - Project Context Document

**Version:** 2.0 (Reflecting state up to v39)
**Last Updated:** June 1, 2025

## 1. Project Overview

### 1.1. Project Summary
The Floor Plan Analyzer is an AI-powered web application designed to provide comprehensive analysis and insights for floor plans. It assists users in evaluating various aspects of a property's layout, including functionality, design, compliance with standards like Vastu, and optimization potential. The tool leverages multiple Large Language Models (LLMs) to generate detailed reports.

### 1.2. Target Audience and Intended Use Cases
*   **Property Buyers:** To understand the pros and cons of a floor plan before making a purchase.
*   **Real Estate Professionals:** To provide clients with detailed analyses and highlight property features.
*   **Architects and Designers:** To get AI-driven feedback on designs, identify potential flaws, and explore optimization opportunities.
*   **Homeowners:** To analyze their current living space for renovation ideas or to understand its Vastu compliance.

**Use Cases:**
*   Uploading a floor plan image (JPG, PNG) or PDF for automated analysis.
*   Receiving a structured report covering room details, Vastu compliance, design flaws, lighting, ventilation, space optimization, accessibility, storage, circulation, and functionality.
*   Interacting with an AI chat interface to ask specific questions about the analyzed floor plan.
*   Testing the connectivity and availability of various integrated AI models.
*   Viewing and analyzing sample floor plans.
*   Exporting analysis results to PDF.
*   Sharing analysis results via a unique link.

---

## 2. Codebase Structure

The project follows a standard Next.js App Router structure.

### 2.1. Folder Structure
*   **`app/`**: Core directory for the Next.js App Router.
    *   **`layout.tsx`**: Root layout component, applies to all routes. Includes global styles, theme provider, and navbar.
    *   **`page.tsx`**: Entry point for the main landing page.
    *   **`globals.css`**: Global styles, including Tailwind CSS base styles and custom global styles.
    *   **`api/`**: Contains backend API route handlers.
        *   `analyze/route.ts`: Handles non-streaming floor plan analysis requests.
        *   `analyze-stream/route.ts`: Handles streaming floor plan analysis requests.
        *   `chat-analysis/route.ts`: Handles AI chat interactions related to an analysis.
        *   `check-env/route.ts`: Checks the availability of necessary environment variables (API keys).
        *   `test-model/route.ts`: Tests connectivity to a specific AI model.
    *   **`analysis/[id]/page.tsx`**: Dynamic route to display detailed analysis results for a specific floor plan.
    *   **`samples/page.tsx`**: Page to display and interact with sample floor plans.
    *   **`model-tester/page.tsx`**: Page for testing all integrated AI models.
    *   **`env-setup/page.tsx`**: Page providing instructions to set up environment variables.
    *   Other route-specific directories (e.g., `status/`, `test/`, `test-enhanced/`).
*   **`components/`**: Contains reusable React components.
    *   **`ui/`**: Shadcn/ui components (e.g., Button, Card, Input). These are typically installed via CLI and not manually written.
    *   `navbar.tsx`: Navigation bar component.
    *   `upload-section.tsx`: Component for handling floor plan uploads.
    *   `analysis-results.tsx`: Component to display analysis results.
    *   `model-tester.tsx`: UI for the AI model testing functionality.
    *   `floor-plan-viewer.tsx`: Component for displaying and interacting with floor plan images.
    *   `env-setup-helper.tsx`: Component providing UI and instructions for environment variable setup.
    *   Other specific UI components (e.g., `hero-section.tsx`, `feature-grid.tsx`).
*   **`lib/`**: Contains utility functions, library configurations, and core logic.
    *   `utils.ts`: General utility functions, including `cn` for conditional class names.
    *   `analysis-prompt.ts`: Logic for generating contextual prompts for LLM analysis.
    *   `analysis-schema.ts`: Zod schemas for validating AI analysis responses.
    *   `analyze-floor-plan.ts`: Core logic for interacting with LLMs for floor plan analysis.
    *   `env-check.ts`: Functions to check and report the status of environment variables.
    *   `pdf-export.ts`: Logic for generating PDF reports from analysis data.
*   **`public/`**: Contains static assets like images, fonts, and icons.
    *   `/placeholder.svg`: Used for generating placeholder images.
*   **`hooks/`**: Custom React hooks (e.g., `use-mobile.tsx`, `use-toast.ts`).
*   **`tailwind.config.ts`**: Tailwind CSS configuration file.
*   **`next.config.mjs`**: Next.js configuration file.
*   **`tsconfig.json`**: TypeScript configuration file.
*   **`package.json`**: Project dependencies and scripts.
*   **`.env.local` (or similar, gitignored)**: Stores environment variables locally, including API keys.

### 2.2. Major Components and Modules
*   **Analysis Engine (`lib/analyze-floor-plan.ts`, `app/api/analyze/...`)**:
    *   Functionality: Takes floor plan data (image URL or base64) and user-selected model, constructs a prompt using `lib/analysis-prompt.ts`, sends it to the chosen LLM via Vercel AI SDK, and processes the response.
    *   Interdependencies: `lib/analysis-prompt.ts`, `lib/analysis-schema.ts`, Vercel AI SDK, specific AI provider SDKs (e.g., `@ai-sdk/openai`).
*   **Prompt Generation (`lib/analysis-prompt.ts`)**:
    *   Functionality: Creates tailored prompts for different analysis aspects (Vastu, modern design, etc.) and property types. Aims to elicit structured JSON output from LLMs.
*   **Schema Validation (`lib/analysis-schema.ts`)**:
    *   Functionality: Uses Zod to define expected structures for LLM responses. Validates incoming data to ensure type safety and data integrity.
*   **Model Tester (`app/model-tester/page.tsx`, `components/model-tester.tsx`, `app/api/test-model/route.ts`)**:
    *   Functionality: Allows users to test the connectivity and basic functionality of all integrated AI models. Provides feedback on success/failure and response times.
*   **Environment Variable Check (`lib/env-check.ts`, `app/api/check-env/route.ts`)**:
    *   Functionality: Verifies if necessary API keys are present in the environment. Guides users if keys are missing.

### 2.3. Code Snippets / Examples

**Example: API call to an AI model (simplified from `lib/analyze-floor-plan.ts`)**
\`\`\`typescript
// lib/analyze-floor-plan.ts (Conceptual)
import { generateText, tool } from 'ai';
import { openai } from '@ai-sdk/openai'; // Or other providers
import { z } from 'zod';
import { analysisSchema } from './analysis-schema'; // Zod schema for validation
import { generateContextualPrompt } from './analysis-prompt';

export async function analyzeFloorPlanWithLLM(
  imageData: string, // base64 image
  modelId: string,
  apiKey: string, // API key for the selected provider
  // ... other params
) {
  const prompt = generateContextualPrompt(imageData, /* ... */);
  const provider = getAIProvider(modelId, apiKey); // Helper to get configured provider

  try {
    const { text, toolResults, finishReason, usage } = await generateText({
      model: provider,
      prompt: prompt,
      tools: {
        analysisResult: tool({
          description: 'Detailed floor plan analysis results.',
          parameters: analysisSchema, // Use Zod schema for tool parameters
          execute: async (args) => args, // Simply return the arguments
        }),
      },
    });

    if (toolResults && toolResults.length > 0 && toolResults[0].result) {
      // Validate and return the structured data
      const validatedData = analysisSchema.parse(toolResults[0].result);
      return { success: true, data: validatedData };
    }
    // Handle cases where the tool wasn't called, or text response is preferred
    return { success: true, data: { summary: text } }; // Simplified fallback

  } catch (error) {
    console.error(`Error analyzing with ${modelId}:`, error);
    return { success: false, error: (error as Error).message };
  }
}
\`\`\`

---

## 3. Features and Functionalities

(This section is largely based on the existing comprehensive feature list, with status indicators.)

### 🎯 Core Analysis Features (100% Complete)
1.  **AI-Powered Floor Plan Analysis** ✅
    *   Description: Users upload a floor plan image (JPG, PNG, PDF). The system uses a selected AI model to analyze the floor plan based on various criteria.
    *   Data Flow: Image upload -> Preprocessing (if any) -> Prompt generation -> AI model API call -> Response parsing & validation -> Display results.
    *   Expected Outcome: Structured JSON output with detailed insights, displayed in a user-friendly format.
2.  **Room Identification & Measurements** ✅
    *   Description: AI identifies individual rooms, classifies them (e.g., bedroom, kitchen), and attempts to extract dimensions and calculate areas. Maps room connectivity.
3.  **Vastu Compliance Analysis** ✅
    *   Description: Assesses the floor plan against traditional Indian architectural principles of Vastu Shastra. Evaluates room placements and directional compliance.
4.  **Design Flaw Detection** ✅
    *   Description: Identifies potential design issues (e.g., poor circulation, awkward spaces, privacy concerns). Classifies severity and offers improvement suggestions.
5.  **Natural Light & Ventilation Analysis** ✅
    *   Description: Evaluates window distribution, potential for cross-ventilation, and overall natural lighting for different rooms.
6.  **Space Optimization Suggestions** ✅
    *   Description: Scores the efficiency of space utilization and provides recommendations for layout improvements.

### 🏗️ Advanced Analysis Features (100% Complete)
7.  **Accessibility Features Analysis** ✅
    *   Description: Assesses features relevant to accessibility, such as wheelchair manoeuvrability, doorway widths, and level changes.
8.  **Storage & Organization Analysis** ✅
    *   Description: Identifies dedicated storage spaces, evaluates their sufficiency, and assesses organization potential.
9.  **Circulation & Flow Analysis** ✅
    *   Description: Evaluates traffic patterns within the floor plan, identifies potential bottlenecks, and assesses entry points.
10. **Functionality & Ergonomics** ✅
    *   Description: Analyzes aspects like kitchen layout efficiency, privacy levels between rooms, and potential noise issues.

### 🎨 User Interface & Experience (95% Complete)
11. **Modern, Mobile-Responsive Design** ✅
    *   Description: UI adapts to various screen sizes using Tailwind CSS. Touch-friendly interactions for mobile users.
12. **Accessibility Compliance (WCAG 2.1 AA)** ✅
    *   Description: Implements ARIA labels, keyboard navigation, screen reader compatibility, high contrast mode considerations, and focus management.
13. **Modern Animations & Transitions** ✅
    *   Description: Smooth page transitions, loading animations, hover effects. Supports reduced motion preferences.
14. **Interactive Floor Plan Viewer** ✅
    *   Description: Allows users to zoom and potentially rotate uploaded floor plans. May include overlays for highlighting analyzed rooms or features.
    *   *[Screenshot Placeholder: Floor plan viewer UI]*
15. **AI Chat Interface** ✅
    *   Description: Users can ask follow-up questions about the analysis results in a chat-like interface. The AI provides context-aware responses.
    *   Data Flow: User query -> API call to `app/api/chat-analysis/route.ts` with analysis context -> LLM processes query -> Streamed response to UI.
    *   *[Screenshot Placeholder: AI Chat UI]*

### 🔧 Technical Features (100% Complete)
16. **Multiple AI Model Support** ✅
    *   Description: Supports various LLMs from providers like OpenAI, Anthropic, Google, Groq, Mistral, DeepSeek, Qwen, and OpenRouter. Users can select their preferred model.
17. **Model Testing & Validation** ✅
    *   Description: A dedicated page (`/model-tester`) allows users to test the connectivity and basic functionality of all integrated AI models.
    *   *[Screenshot Placeholder: Model Tester UI]*
18. **Real-time Streaming Analysis** ✅
    *   Description: Analysis results and chat responses are streamed to the UI for a more responsive user experience.
19. **Sample Floor Plans Gallery** ✅
    *   Description: A gallery of pre-loaded sample floor plans that users can analyze to see the tool's capabilities.
20. **Multi-file Upload Support** ✅
    *   Description: Users can upload floor plans in JPG, PNG, and PDF formats. Includes drag & drop functionality and file validation.

### 📊 Data & Export Features (100% Complete)
21. **Comprehensive Analysis Schema (`lib/analysis-schema.ts`)** ✅
    *   Description: Uses Zod to define and validate the structure of AI-generated analysis data, ensuring consistency and type safety.
22. **PDF Export Functionality** ✅
    *   Description: Users can export the detailed analysis report, including visualizations, as a PDF document.
23. **Sharing Functionality** ✅
    *   Description: Generated analysis can be shared via a unique link.
24. **Analysis Results Dashboard** ✅
    *   Description: Presents analysis results in a structured, tabbed interface with visual aids where appropriate.

### 🔐 Security & Infrastructure (100% Complete)
25. **Environment Variable Management** ✅
    *   Description: Secure handling of API keys and other sensitive configurations using environment variables. Includes an `env-setup` page for guidance.
26. **Error Handling & User Guidance** ✅
    *   Description: Provides comprehensive error messages and guidance, especially for API key setup and model connectivity issues.

### 🚧 IN PROGRESS FEATURES (1/28)
27. **Advanced Mobile Optimizations** 🔄 (95% Complete)
    *   Remaining: Fine-tuning touch gestures for the floor plan viewer.

### 📋 PLANNED FEATURES (1/28)
28. **User Authentication & Accounts** 📅
    *   Features: User registration/login, saved analysis history, personal dashboards.

---

## 4. Technical Stack

*   **Frontend Framework:** Next.js (v14+ - Assumed latest stable) with App Router
*   **UI Library:** React (v18+ - Assumed latest stable)
*   **Styling:** Tailwind CSS (v3+)
*   **UI Components:** Shadcn/UI
*   **Icons:** Lucide React
*   **AI Integration:** Vercel AI SDK (`ai` package, `@ai-sdk/*` provider packages)
*   **Schema Validation:** Zod
*   **Deployment Platform:** Vercel
*   **Language:** TypeScript
*   **Version Control:** Git

**Rationale:**
*   **Next.js & React:** Industry standard for modern, performant web applications with excellent developer experience and SSR/SSG capabilities. App Router for modern routing and server components.
*   **Tailwind CSS & Shadcn/UI:** Rapid UI development with utility-first CSS and accessible, customizable components.
*   **Vercel AI SDK:** Simplifies integration with various LLMs, providing a unified API for streaming, tool usage, and model configuration.
*   **TypeScript:** Enhances code quality and maintainability with static typing.
*   **Zod:** Robust schema declaration and validation, crucial for handling LLM outputs.
*   **Vercel Platform:** Seamless deployment, CI/CD, serverless functions, and environment variable management, optimized for Next.js.

---

## 5. Data Storage and Management

### 5.1. LLM Prompts
*   **Storage:** LLM prompts are primarily constructed dynamically in `lib/analysis-prompt.ts`. Core prompt templates or logic might be embedded as strings or functions within this module.
*   **Customization:** Prompts are contextual, considering factors like property type, region (if applicable), and specific analysis focus (e.g., Vastu, modern design).

### 5.2. API Keys and Sensitive Information
*   **Storage:** API keys for various LLM providers (OpenAI, Anthropic, Google, Groq, Mistral, DeepSeek, Qwen, OpenRouter) are stored as environment variables.
*   **Local Development:** Stored in a `.env.local` file (gitignored).
*   **Deployment (Vercel):** Stored in Vercel project environment variable settings.
*   **Access:** API keys are accessed exclusively on the server-side (within API routes like `app/api/analyze/route.ts` or `app/api/test-model/route.ts`) and are never exposed to the client.

### 5.3. Data Models and Schemas
*   **Primary Schema:** `lib/analysis-schema.ts` defines Zod schemas for the expected structure of the floor plan analysis JSON output from LLMs. This includes schemas for overall summary, room details, Vastu analysis, design flaws, etc.
    \`\`\`typescript
    // lib/analysis-schema.ts (Conceptual Snippet)
    import { z } from 'zod';

    export const roomSchema = z.object({
      name: z.string(),
      dimensions: z.string().optional(),
      area: z.string().optional(),
      // ... other room properties
    });

    export const vastuAnalysisSchema = z.object({
      overallScore: z.number().min(0).max(100),
      // ... other Vastu details
    });

    export const analysisSchema = z.object({
      summary: z.string(),
      rooms: z.array(roomSchema),
      vastu: vastuAnalysisSchema.optional(),
      // ... other top-level analysis sections
    });
    \`\`\`
*   **Uploaded Data:** User-uploaded floor plan images are temporarily processed (e.g., converted to base64 for API transmission) but not persistently stored long-term unless a future feature for saved analyses is implemented with explicit user consent and a proper storage solution (e.g., Vercel Blob, S3).

---

## 6. API Integrations

The project integrates with multiple LLM providers via the Vercel AI SDK.

*   **Providers & Models (Examples - refer to `lib/analyze-floor-plan.ts` and `app/api/test-model/route.ts` for the current list):**
    *   **OpenAI:** GPT-4o, GPT-4.1, GPT-4.1-mini
        *   Endpoint: Handled by `@ai-sdk/openai`
        *   Auth: Bearer Token (OpenAI API Key via `OPENAI_API_KEY` env var)
    *   **Anthropic:** Claude 3.5 Sonnet
        *   Endpoint: Handled by `@ai-sdk/anthropic`
        *   Auth: API Key (Anthropic API Key via `ANTHROPIC_API_KEY` env var)
    *   **Google:** Gemini 2.0 Flash, Gemini 2.5 Flash, Gemini 2.5 Pro
        *   Endpoint: Handled by `@ai-sdk/google` or `@ai-sdk/google-vertex`
        *   Auth: API Key (Google API Key via `GOOGLE_GENERATIVE_AI_API_KEY` env var)
    *   **Groq:** Llama 4 Scout
        *   Endpoint: Handled by `@ai-sdk/groq`
        *   Auth: API Key (Groq API Key via `GROQ_API_KEY` env var)
    *   **Mistral:** Mistral Large, Mistral Small, Mistral Medium
        *   Endpoint: Handled by `@ai-sdk/mistral`
        *   Auth: API Key (Mistral API Key via `MISTRAL_API_KEY` env var)
    *   **DeepSeek:** DeepSeek Chat/V3
        *   Endpoint: Handled by `@ai-sdk/deepseek` (or generic provider if specific SDK not available)
        *   Auth: API Key (DeepSeek API Key via `DEEPSEEK_API_KEY` env var)
    *   **Qwen (Alibaba Cloud):** Qwen 2.5 Max
        *   Endpoint: Handled by `@ai-sdk/alibaba` (or generic provider)
        *   Auth: API Key (Qwen API Key via `QWEN_API_KEY` env var)
    *   **OpenRouter:** Various models (GPT-4o, Claude, Gemini, Qwen VL, Llama Vision)
        *   Endpoint: Handled by `@ai-sdk/openrouter`
        *   Auth: API Key (OpenRouter API Key via `OPENROUTER_API_KEY` env var)

*   **Interaction:**
    *   The `generateText` function from the `ai` package is primarily used.
    *   Tools are defined using Zod schemas (`lib/analysis-schema.ts`) to encourage structured JSON output from LLMs.
    *   Streaming is used for analysis results and chat responses to improve UX.

*   **Error Handling & Rate Limiting:**
    *   API calls are wrapped in try-catch blocks within server-side route handlers.
    *   Errors from AI providers are caught and relayed to the client with user-friendly messages.
    *   The Model Tester helps diagnose connectivity issues.
    *   Rate limiting is primarily handled by the respective AI providers. The application should gracefully handle rate limit errors (e.g., by informing the user). No custom rate limiting is implemented in the application itself.

---

## 7. Challenges and Limitations

### 7.1. Challenges Encountered
*   **Prompt Engineering:** Crafting effective prompts that consistently elicit accurate and structured JSON responses across different LLMs.
*   **Multi-Model Management:** Ensuring compatibility and consistent performance across a diverse range of AI models and their APIs.
*   **API Key Security & Management:** Guiding users to securely manage their API keys and ensuring the application handles them safely.
*   **Error Handling:** Robustly handling various errors from different AI APIs (network issues, authentication failures, model errors, rate limits).
*   **Schema Evolution:** Managing changes to the `analysisSchema` as new analysis features are added or LLM capabilities evolve, while maintaining backward compatibility if needed.
*   **Image Processing for LLMs:** Ensuring images are in a suitable format (e.g., base64 string) and resolution for vision-enabled LLMs.

### 7.2. Current Limitations
*   **Dependency on Third-Party APIs:** The application's core functionality relies on the availability and performance of external LLM services. Outages or changes in these services can impact the application.
*   **API Costs:** Usage of LLM APIs incurs costs, which are borne by the user (via their API keys).
*   **Analysis Accuracy:** The accuracy and quality of the analysis depend on the capabilities of the chosen LLM and the effectiveness of the prompts.
*   **Complex Floor Plans:** Very complex or poorly drawn floor plans might be challenging for LLMs to interpret accurately.
*   **No Persistent User Data:** Currently, analyses are not saved per user account (planned feature). Each session is new.

### 7.3. Known Bugs
*   **Model-Specific Failures:** Some models might occasionally fail due to API issues, rate limits on the user's key, or if the model is temporarily unavailable. The Model Tester page helps identify these. (e.g., Gemini 2.5 Pro often requires a paid tier).
*   **DeepSeek/Qwen Model Names:** Exact model names for DeepSeek and Qwen can sometimes be tricky and might require specific user access or key permissions. The current setup attempts to use common ones.
*   Refer to the Model Tester page (`/model-tester`) for the most up-to-date status on individual model connectivity.

---

## 8. Future Enhancements

### 8.1. Planned Features (from Roadmap)
*   **User Authentication & Accounts:**
    *   User registration and login.
    *   Persistent storage of analysis history.
    *   Personal dashboards.
    *   Saved floor plan collections.
*   **Advanced Mobile Optimizations:**
    *   Fine-tuning touch gestures for the floor plan viewer (currently in progress).
*   **Batch Processing Capabilities:** Allow users to upload and analyze multiple floor plans simultaneously.
*   **Cost Tracking and Analytics (for API usage):** Help users estimate or track their API costs.
*   **Advanced Sharing and Collaboration:** More granular sharing options, potentially team features.
*   **API for Third-Party Integrations:** Allow other services to integrate with the Floor Plan Analyzer.

### 8.2. Potential Areas for Optimization and Scalability
*   **Prompt Optimization:** Continuously refine prompts for better accuracy and efficiency (e.g., reducing token usage).
*   **Caching Strategies:** Implement caching for frequently accessed data or sample analyses (if applicable).
*   **Image Preprocessing:** More advanced image preprocessing on the client or server to optimize images for LLM analysis.
*   **Database Integration:** For user accounts and saved analyses, choose a scalable database solution (e.g., Supabase, Neon, Vercel Postgres).

### 8.3. Ideas for Future Development
*   **3D Floor Plan Analysis:** Extend capabilities to analyze 3D models.
*   **Integration with CAD Tools:** Allow direct import from architectural software.
*   **Material and Cost Estimation:** Leverage AI to estimate materials and construction costs based on the floor plan.
*   **Comparative Analysis:** Allow users to compare two different floor plans side-by-side.
*   **Customizable Analysis Profiles:** Allow users to define their own analysis priorities or checklists.

---

## 9. LLM Interaction Details

### 9.1. Prompts
*   **Generation:** Prompts are dynamically generated by `lib/analysis-prompt.ts`.
*   **Structure:** Prompts are carefully structured to request specific information and encourage JSON output. They typically include:
    *   A role definition for the AI (e.g., "You are an expert floor plan analyst...").
    *   The floor plan data (often as a base64 image string for vision models).
    *   Specific instructions on what aspects to analyze (e.g., "Analyze Vastu compliance...", "Identify design flaws...").
    *   A request for output in a specific JSON format, often referencing the tool schema.
*   **Example Snippet (Conceptual from `lib/analysis-prompt.ts`):**
    \`\`\`typescript
    // function generateContextualPrompt(imageData: string, analysisType: string) {
    //   let specificInstructions = "";
    //   if (analysisType === "vastu") {
    //     specificInstructions = "Focus on Vastu Shastra principles. Evaluate room placements, entrances, and directional aspects. Provide a Vastu score and recommendations.";
    //   } else {
    //     specificInstructions = "Provide a comprehensive modern design analysis. Focus on functionality, flow, lighting, and potential design flaws.";
    //   }
    //   return `
    //     You are an expert floor plan analyst. Analyze the following floor plan image.
    //     Floor plan image: [Image data will be here or handled by multimodal input]
    //     ${specificInstructions}
    //     Please provide your analysis in a structured JSON format using the 'analysisResult' tool.
    //   `;
    // }
    \`\`\`

### 9.2. Response Processing
*   **Tool Usage:** The Vercel AI SDK's `generateText` function is used with a defined `tool` (e.g., `analysisResult`) whose parameters are defined by a Zod schema (`lib/analysis-schema.ts`).
*   **Validation:** The LLM's structured output (arguments for the tool call) is automatically parsed by the AI SDK. If a tool is successfully called, its `result` (which we set up to be the arguments themselves) is then validated against the Zod schema.
    \`\`\`typescript
    // Conceptual processing after generateText call
    // if (toolResults && toolResults[0]?.result) {
    //   const validatedData = analysisSchema.parse(toolResults[0].result);
    //   // Use validatedData in the UI
    // } else if (text) {
    //   // Handle plain text response as a fallback
    // }
    \`\`\`
*   **Streaming:** For chat and analysis results, responses are streamed to the client using Next.js API routes and client-side hooks (e.g., `useChat` or `useCompletion` from the Vercel AI SDK, or custom streaming logic).

### 9.3. Handling LLM Errors
*   **API Errors:** Network errors, authentication failures, rate limit errors, and invalid request errors from LLM providers are caught in server-side API routes.
*   **User Feedback:** Errors are translated into user-friendly messages displayed in the UI (e.g., "Failed to connect to [Model Name]: [Error details]").
*   **Model Unavailability:** The Model Tester helps identify if a specific model is down or if the user's API key lacks access.
*   **Unexpected Behavior/Output:**
    *   If the LLM fails to call the tool or provide structured JSON, the application might fall back to displaying any plain text response.
    *   Zod validation errors (if the LLM output doesn't match the schema) are caught, and an error is typically shown to the user, or a simplified summary is attempted.
    *   Ongoing prompt refinement aims to minimize such issues.

---

## 10. Deployment and Infrastructure

### 10.1. Deployment Environment
*   **Platform:** Vercel
*   **Workflow:** Continuous deployment is typically set up via Git integration. Pushes to the main branch (or a designated production branch) trigger automatic builds and deployments on Vercel.

### 10.2. Configuration Details and Environment Variables
*   **Environment Variables:** All sensitive information (API keys) and some configurations are managed as environment variables.
    *   `OPENAI_API_KEY`
    *   `ANTHROPIC_API_KEY`
    *   `GOOGLE_GENERATIVE_AI_API_KEY`
    *   `GROQ_API_KEY`
    *   `MISTRAL_API_KEY`
    *   `DEEPSEEK_API_KEY`
    *   `QWEN_API_KEY`
    *   `OPENROUTER_API_KEY`
    *   `NEXT_PUBLIC_APP_URL` (Optional, for absolute URLs if needed)
*   **Local:** Managed in `.env.local`.
*   **Vercel:** Managed in Project Settings > Environment Variables. These are securely injected into the build and runtime environments.

### 10.3. Monitoring and Logging
*   **Vercel Analytics:** Provides insights into traffic, performance, and user demographics.
*   **Vercel Logs:** Real-time logs for serverless functions (API routes) and build processes, accessible via the Vercel dashboard.
*   **Client-Side Error Tracking (Potential):** Could integrate a service like Sentry or LogRocket for more detailed client-side error reporting (not explicitly implemented yet).
*   **Server-Side Logging:** `console.log`, `console.error` within API routes are captured by Vercel Logs.

---

## 11. Version Control

### 11.1. System
*   **Git:** Used for version control.
*   **Repository Hosting (Assumed):** GitHub, GitLab, or Bitbucket.

### 11.2. Branching Strategy (Recommended Best Practice)
*   **`main` (or `master`):** Represents the production-ready code. Deployments to production are made from this branch.
*   **`develop`:** Integration branch for features. When stable, merged into `main`.
*   **Feature Branches (e.g., `feature/user-auth`, `fix/model-tester-bug`):** Created from `develop` for new features or bug fixes. Merged back into `develop` via Pull/Merge Requests.
*   **Release Branches (Optional):** For preparing releases (e.g., `release/v2.1`).

### 11.3. Commit Guidelines (Recommended Best Practice)
*   **Conventional Commits:** Encouraged for clear and automated changelog generation (e.g., `feat: add pdf export button`, `fix: resolve model connection issue`).
*   **Atomic Commits:** Each commit should represent a single logical change.
*   **Descriptive Messages:** Commit messages should clearly explain the "what" and "why" of the change.

---

## 12. Security Considerations

### 12.1. Security Measures Implemented
*   **API Key Management:**
    *   API keys are stored as environment variables.
    *   Accessed only on the server-side (API routes).
    *   Never exposed to the client-side browser.
    *   The `/env-setup` page guides users on secure local setup.
*   **HTTPS:** Enforced by Vercel for all deployed applications.
*   **Input Validation (Implicit):**
    *   File uploads have basic type/size validation.
    *   Zod schemas validate the structure of data received from LLMs.
*   **Cross-Site Scripting (XSS) Prevention:** React inherently helps prevent XSS by escaping string content rendered in JSX. Care is taken if `dangerouslySetInnerHTML` is ever used (currently avoided).
*   **Cross-Site Request Forgery (CSRF) Prevention:** Next.js API routes, when used with standard methods, have some built-in protections. For form submissions leading to state changes, CSRF tokens would be a standard consideration (more relevant with user authentication).

### 12.2. Potential Security Vulnerabilities and Mitigation Strategies
*   **Insecure API Key Exposure (User Error):**
    *   Vulnerability: Users might accidentally commit `.env.local` or expose their keys.
    *   Mitigation: Strong `.gitignore` for `.env*.local`. Clear guidance on the `/env-setup` page.
*   **Prompt Injection:**
    *   Vulnerability: Malicious user input embedded in prompts could potentially manipulate LLM behavior.
    *   Mitigation: While difficult to completely prevent, inputs used in prompts (if any directly from users) should be sanitized or carefully handled. The current application primarily uses user-uploaded images, reducing direct text injection into core prompts. Chat interface inputs are a potential vector if not handled carefully.
*   **Denial of Service (DoS) / API Abuse:**
    *   Vulnerability: Malicious users could attempt to exhaust API quotas by repeatedly triggering analyses.
    *   Mitigation: This is largely reliant on the user's own API key quotas. Future user authentication could allow for per-user rate limiting on application-level resources.
*   **Data Privacy for Uploaded Floor Plans:**
    *   Vulnerability: Users upload potentially sensitive floor plan data.
    *   Mitigation:
        *   Currently, images are sent to third-party LLMs for analysis. Users should be aware of this.
        *   No long-term storage of uploaded images by the application itself (unless this feature is added with clear consent and security measures like encryption at rest).
        *   A privacy policy should be available to users.

---
This document aims to provide a comprehensive understanding of the Floor Plan Analyzer project. It should be kept up-to-date as the project evolves.
