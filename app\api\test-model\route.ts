import { type NextRequest, NextResponse } from "next/server"
import { openai } from "@ai-sdk/openai"
import { anthropic } from "@ai-sdk/anthropic"
import { google } from "@ai-sdk/google"
import { groq } from "@ai-sdk/groq"
import { mistral } from "@ai-sdk/mistral"
import { generateText } from "ai"

export async function POST(request: NextRequest) {
  try {
    const { provider } = await request.json()

    if (!provider) {
      return NextResponse.json({ success: false, error: "No provider specified" }, { status: 400 })
    }

    console.log(`Testing provider: ${provider}`)

    // Define provider configurations with exact API model names
    const providerConfigs: Record<string, { model: any; envKey: string; setupUrl?: string; hint?: string }> = {
      groq: {
        model: groq("meta-llama/llama-4-scout-17b-16e-instruct"),
        envKey: "GROQ_API_KEY",
        setupUrl: "https://console.groq.com/keys",
      },
      openai: {
        model: openai("gpt-4o"),
        envKey: "OPENAI_API_KEY",
        setupUrl: "https://platform.openai.com/account/api-keys",
      },
      "openai-gpt4-1": {
        model: openai("gpt-4.1"),
        envKey: "OPENAI_API_KEY",
        setupUrl: "https://platform.openai.com/account/api-keys",
      },
      "openai-gpt4-1-mini": {
        model: openai("gpt-4.1-mini"),
        envKey: "OPENAI_API_KEY",
        setupUrl: "https://platform.openai.com/account/api-keys",
      },
      anthropic: {
        model: anthropic("claude-3-5-sonnet-********"),
        envKey: "ANTHROPIC_API_KEY",
        setupUrl: "https://console.anthropic.com/settings/keys",
      },
      gemini: {
        model: google("gemini-2.0-flash"),
        envKey: "GOOGLE_GENERATIVE_AI_API_KEY",
        setupUrl: "https://aistudio.google.com/app/apikey",
      },
      "gemini-2-5": {
        model: google("gemini-2.5-flash-preview-05-20"),
        envKey: "GOOGLE_GENERATIVE_AI_API_KEY",
        setupUrl: "https://aistudio.google.com/app/apikey",
      },
      "gemini-2-5-pro": {
        model: google("gemini-2.5-pro-preview-05-06"),
        envKey: "GOOGLE_GENERATIVE_AI_API_KEY",
        setupUrl: "https://aistudio.google.com/app/apikey",
      },
      mistral: {
        model: mistral("mistral-large-latest"),
        envKey: "MISTRAL_API_KEY",
        setupUrl: "https://console.mistral.ai/",
      },
      "mistral-small": {
        model: mistral("mistral-small-latest"),
        envKey: "MISTRAL_API_KEY",
        setupUrl: "https://console.mistral.ai/",
      },
      "mistral-medium": {
        model: mistral("mistral-medium-latest"),
        envKey: "MISTRAL_API_KEY",
        setupUrl: "https://console.mistral.ai/",
      },
      deepseek: {
        model: openai("deepseek-chat", {
          baseURL: "https://api.deepseek.com/v1",
        }),
        envKey: "DEEPSEEK_API_KEY",
        setupUrl: "https://platform.deepseek.com/",
      },
      qwen: {
        model: openai("qwen-max-2025-01-25", {
          baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
        }),
        envKey: "QWEN_API_KEY",
        setupUrl: "https://dashscope.aliyun.com/",
      },
      "openrouter-gpt4o": {
        model: openai("openai/gpt-4o", {
          baseURL: "https://openrouter.ai/api/v1",
        }),
        envKey: "OPENROUTER_API_KEY",
        setupUrl: "https://openrouter.ai/keys",
      },
      "openrouter-claude": {
        model: openai("anthropic/claude-3-5-sonnet", {
          baseURL: "https://openrouter.ai/api/v1",
        }),
        envKey: "OPENROUTER_API_KEY",
        setupUrl: "https://openrouter.ai/keys",
      },
      "openrouter-gemini": {
        model: openai("google/gemma-3-27b-it:free", {
          baseURL: "https://openrouter.ai/api/v1",
        }),
        envKey: "OPENROUTER_API_KEY",
        setupUrl: "https://openrouter.ai/keys",
      },
      "openrouter-qwen": {
        model: openai("qwen/qwen2.5-vl-72b-instruct:free", {
          baseURL: "https://openrouter.ai/api/v1",
        }),
        envKey: "OPENROUTER_API_KEY",
        setupUrl: "https://openrouter.ai/keys",
      },
      "openrouter-llama": {
        model: openai("meta-llama/llama-3.2-90b-vision-instruct", {
          baseURL: "https://openrouter.ai/api/v1",
        }),
        envKey: "OPENROUTER_API_KEY",
        setupUrl: "https://openrouter.ai/keys",
      },
    }

    const config = providerConfigs[provider]

    if (!config) {
      return NextResponse.json(
        {
          success: false,
          error: `Invalid provider: ${provider}`,
          availableProviders: Object.keys(providerConfigs),
        },
        { status: 400 },
      )
    }

    // Check if the environment variable is set
    const apiKey = process.env[config.envKey]
    if (!apiKey || apiKey.trim() === "") {
      return NextResponse.json(
        {
          success: false,
          error: `API key for ${provider} is not set. Please add ${config.envKey} to your environment variables.`,
          envKey: config.envKey,
          setupUrl: config.setupUrl,
          hint: config.hint,
        },
        { status: 400 },
      )
    }

    // Test the model with a simple query
    try {
      // Use generateText from ai package with the provider's model
      const result = await generateText({
        model: config.model,
        prompt: "Say hello in a short sentence.",
        maxTokens: 30,
      })

      return NextResponse.json({
        success: true,
        message: `Successfully connected to ${provider}`,
        sample: result.text,
      })
    } catch (error: any) {
      console.error(`Error testing ${provider}:`, error)
      return NextResponse.json(
        {
          success: false,
          error: `Failed to connect to ${provider}: ${error.message}`,
          originalError: error.message,
          setupUrl: config.setupUrl,
          envKey: config.envKey,
        },
        { status: 500 },
      )
    }
  } catch (error: any) {
    console.error("Error in test-model route:", error)
    return NextResponse.json({ success: false, error: error.message || "An unknown error occurred" }, { status: 500 })
  }
}
