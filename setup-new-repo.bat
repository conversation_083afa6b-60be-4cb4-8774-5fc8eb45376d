@echo off
REM Batch script to set up new GitHub repository for Floor Plan Analyzer
REM Usage: setup-new-repo.bat <github-repo-url>

if "%1"=="" (
    echo Error: Please provide the GitHub repository URL
    echo Usage: setup-new-repo.bat ^<github-repo-url^>
    echo Example: setup-new-repo.bat https://github.com/username/floor-plan-analyzer-v2.git
    pause
    exit /b 1
)

set REPO_URL=%1

echo Setting up new GitHub repository for Floor Plan Analyzer...
echo.

REM Get current directory
set PROJECT_ROOT=%CD%
echo Project root: %PROJECT_ROOT%

REM Create temp directory
set TEMP_DIR=%TEMP%\floor-plan-analyzer-new-repo
if exist "%TEMP_DIR%" (
    echo Removing existing temp directory...
    rmdir /s /q "%TEMP_DIR%"
)

echo Creating temporary directory: %TEMP_DIR%
mkdir "%TEMP_DIR%"

REM Initialize git repo
echo Initializing new git repository...
cd /d "%TEMP_DIR%"
git init

REM Copy files (using xcopy for directories, copy for files)
echo Copying project files...

if exist "%PROJECT_ROOT%\app" xcopy "%PROJECT_ROOT%\app" "%TEMP_DIR%\app" /e /i /h /y
if exist "%PROJECT_ROOT%\components" xcopy "%PROJECT_ROOT%\components" "%TEMP_DIR%\components" /e /i /h /y
if exist "%PROJECT_ROOT%\hooks" xcopy "%PROJECT_ROOT%\hooks" "%TEMP_DIR%\hooks" /e /i /h /y
if exist "%PROJECT_ROOT%\lib" xcopy "%PROJECT_ROOT%\lib" "%TEMP_DIR%\lib" /e /i /h /y
if exist "%PROJECT_ROOT%\public" xcopy "%PROJECT_ROOT%\public" "%TEMP_DIR%\public" /e /i /h /y
if exist "%PROJECT_ROOT%\styles" xcopy "%PROJECT_ROOT%\styles" "%TEMP_DIR%\styles" /e /i /h /y

if exist "%PROJECT_ROOT%\.env.local" copy "%PROJECT_ROOT%\.env.local" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\.gitignore" copy "%PROJECT_ROOT%\.gitignore" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\ADMIN_DASHBOARD_ROADMAP.md" copy "%PROJECT_ROOT%\ADMIN_DASHBOARD_ROADMAP.md" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\components.json" copy "%PROJECT_ROOT%\components.json" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\next.config.mjs" copy "%PROJECT_ROOT%\next.config.mjs" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\package.json" copy "%PROJECT_ROOT%\package.json" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\pnpm-lock.yaml" copy "%PROJECT_ROOT%\pnpm-lock.yaml" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\postcss.config.mjs" copy "%PROJECT_ROOT%\postcss.config.mjs" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\PRD_STATUS.md" copy "%PROJECT_ROOT%\PRD_STATUS.md" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\README.md" copy "%PROJECT_ROOT%\README.md" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\tailwind.config.ts" copy "%PROJECT_ROOT%\tailwind.config.ts" "%TEMP_DIR%\"
if exist "%PROJECT_ROOT%\tsconfig.json" copy "%PROJECT_ROOT%\tsconfig.json" "%TEMP_DIR%\"

echo Adding files to git...
git add .

echo Creating initial commit...
git commit -m "Initial commit: Floor Plan Analyzer v2 - Next.js application with multiple AI model integrations"

echo Adding remote origin...
git remote add origin %REPO_URL%

echo Pushing to GitHub...
git branch -M main
git push -u origin main

echo Creating checkpoint tag...
git tag -a v1.0.0-checkpoint -m "Checkpoint: Initial release of Floor Plan Analyzer v2"
git push origin v1.0.0-checkpoint

echo.
echo ✅ Successfully set up new repository!
echo Repository URL: %REPO_URL%
echo Checkpoint tag: v1.0.0-checkpoint
echo Temp directory: %TEMP_DIR%
echo.
echo You can now delete the temp directory if everything looks good.

REM Return to original directory
cd /d "%PROJECT_ROOT%"

pause
