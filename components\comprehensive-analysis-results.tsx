"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { CheckCircle, AlertTriangle, Home, Eye, Zap, Shield } from "lucide-react"
import type { ComprehensiveAnalysisResult } from "@/lib/analysis-schema"

interface ComprehensiveAnalysisResultsProps {
  analysisData: ComprehensiveAnalysisResult
  projectInfo: any
}

export function ComprehensiveAnalysisResults({ analysisData, projectInfo }: ComprehensiveAnalysisResultsProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Home className="w-5 h-5" />
            {projectInfo.projectName || "Floor Plan Analysis"}
          </CardTitle>
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline">{analysisData.overallAnalysis.dwellingType || "Unknown Type"}</Badge>
            <Badge variant="secondary">{analysisData.overallAnalysis.layoutStyle || "Standard Layout"}</Badge>
            <Badge variant="outline">{analysisData.overallAnalysis.estimatedTotalAreaCategory || "Medium"} Size</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">{analysisData.overallAnalysis.generalImpression}</p>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="rooms">Rooms</TabsTrigger>
          <TabsTrigger value="circulation">Flow</TabsTrigger>
          <TabsTrigger value="light">Light & Air</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Key Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Bedrooms:</span>
                  <span className="font-semibold">{analysisData.keyFeaturesChecklist.numBedrooms || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span>Full Bathrooms:</span>
                  <span className="font-semibold">{analysisData.keyFeaturesChecklist.numBathroomsFull || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span>Balconies:</span>
                  <span className="font-semibold">
                    {analysisData.keyFeaturesChecklist.numBalconiesPatiosDecks || "0"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Open Plan:</span>
                  <span className="font-semibold">
                    {analysisData.keyFeaturesChecklist.isOpenPlanLivingDiningKitchen ? "Yes" : "No"}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Circulation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Flow Efficiency:</span>
                  <Badge variant={analysisData.circulation.flowEfficiency === "High" ? "default" : "secondary"}>
                    {analysisData.circulation.flowEfficiency || "N/A"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Entry Points:</span>
                  <span className="font-semibold">{analysisData.circulation.mainEntryPoints || "N/A"}</span>
                </div>
                <div className="text-sm text-gray-600">{analysisData.circulation.primaryPathwaysDescription}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Storage</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Storage Spaces:</span>
                  <span className="font-semibold">{analysisData.storage.dedicatedStorageSpaces.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Sufficiency:</span>
                  <Badge variant="outline">{analysisData.storage.overallStorageSufficiency || "N/A"}</Badge>
                </div>
                {analysisData.storage.dedicatedStorageSpaces.map((storage, index) => (
                  <div key={index} className="text-sm text-gray-600">
                    {storage.storageType} ({storage.sizeCategory})
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rooms" className="space-y-4">
          {analysisData.rooms.map((room) => (
            <Card key={room.roomId}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{room.roomNameLabel || room.roomId}</span>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{room.primaryFunction || "Unknown"}</Badge>
                    <Badge variant="outline">{room.estimatedSizeCategory || "Medium"}</Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-600">Doors</p>
                    <p className="font-semibold">{room.numDoors || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Windows</p>
                    <p className="font-semibold">{room.numWindows || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Natural Light</p>
                    <p className="font-semibold">{room.hasNaturalLight ? "Yes" : "No"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Exterior Access</p>
                    <p className="font-semibold">{room.hasDirectExteriorAccess ? "Yes" : "No"}</p>
                  </div>
                </div>

                {room.furnitureFixtures.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-600 mb-2">Furniture & Fixtures:</p>
                    <div className="flex gap-2 flex-wrap">
                      {room.furnitureFixtures.map((item, index) => (
                        <Badge key={index} variant="outline">
                          {item}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {room.connections.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-600 mb-2">Connected to:</p>
                    <div className="flex gap-2 flex-wrap">
                      {room.connections.map((connection, index) => (
                        <Badge key={index} variant="secondary">
                          {connection}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {room.specificObservations && <p className="text-sm text-gray-600">{room.specificObservations}</p>}
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="circulation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Circulation Analysis
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Flow Efficiency</h4>
                <Badge variant={analysisData.circulation.flowEfficiency === "High" ? "default" : "secondary"}>
                  {analysisData.circulation.flowEfficiency || "Not Assessed"}
                </Badge>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Primary Pathways</h4>
                <p className="text-sm text-gray-600">{analysisData.circulation.primaryPathwaysDescription}</p>
              </div>

              {analysisData.circulation.potentialBottlenecks.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-2 text-orange-600">Potential Bottlenecks</h4>
                  <ul className="space-y-1">
                    {analysisData.circulation.potentialBottlenecks.map((bottleneck, index) => (
                      <li key={index} className="flex items-center gap-2 text-sm">
                        <AlertTriangle className="w-4 h-4 text-orange-600" />
                        {bottleneck}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="light" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Natural Light & Ventilation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Window Distribution</h4>
                <p className="text-sm text-gray-600">
                  {analysisData.naturalLightAndVentilation.overallWindowDistribution}
                </p>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Cross Ventilation</h4>
                <Badge
                  variant={
                    analysisData.naturalLightAndVentilation.crossVentilationPotential === "Good"
                      ? "default"
                      : "secondary"
                  }
                >
                  {analysisData.naturalLightAndVentilation.crossVentilationPotential || "Not Assessed"}
                </Badge>
              </div>

              {analysisData.naturalLightAndVentilation.roomsWithAbundantLight.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-2 text-green-600">Well-Lit Rooms</h4>
                  <div className="flex gap-2 flex-wrap">
                    {analysisData.naturalLightAndVentilation.roomsWithAbundantLight.map((room, index) => (
                      <Badge key={index} variant="outline" className="border-green-500 text-green-700">
                        {room}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {analysisData.naturalLightAndVentilation.roomsWithLimitedLight.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-2 text-orange-600">Limited Light Rooms</h4>
                  <div className="flex gap-2 flex-wrap">
                    {analysisData.naturalLightAndVentilation.roomsWithLimitedLight.map((room, index) => (
                      <Badge key={index} variant="outline" className="border-orange-500 text-orange-700">
                        {room}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Accessibility & Features
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Accessibility Features</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Wheelchair-friendly doorways:</span>
                    <span>
                      {analysisData.accessibilityFeaturesObserved.doorwaysAppearWideEnoughForWheelchair ? "Yes" : "No"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Level changes:</span>
                    <span>{analysisData.accessibilityFeaturesObserved.levelChangesWithinUnit || "None visible"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Bathroom maneuverability:</span>
                    <span>
                      {analysisData.accessibilityFeaturesObserved.mainBathroomManeuverabilitySpace || "Not assessed"}
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Special Features</h4>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex justify-between">
                    <span>Master Suite:</span>
                    <span>{analysisData.keyFeaturesChecklist.hasMasterSuite ? "Yes" : "No"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Dedicated Laundry:</span>
                    <span>{analysisData.keyFeaturesChecklist.hasDedicatedLaundry ? "Yes" : "No"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fireplace:</span>
                    <span>{analysisData.keyFeaturesChecklist.hasFireplace ? "Yes" : "No"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Kitchen Island:</span>
                    <span>{analysisData.keyFeaturesChecklist.hasKitchenIsland ? "Yes" : "No"}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-green-600">Positive Aspects</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {analysisData.positiveAspects.map((aspect, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm">{aspect}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-orange-600">Areas for Improvement</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analysisData.potentialIssuesAndImprovements.map((item, index) => (
                  <div key={index} className="border-l-4 border-orange-500 pl-3">
                    <div className="flex items-center gap-2 mb-1">
                      <AlertTriangle className="w-4 h-4 text-orange-600" />
                      <span className="font-medium">{item.issue}</span>
                    </div>
                    {item.suggestion && <p className="text-sm text-blue-600">Suggestion: {item.suggestion}</p>}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {analysisData.unidentifiableSpaces.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-gray-600">Unidentifiable Spaces</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {analysisData.unidentifiableSpaces.map((space, index) => (
                    <div key={index} className="text-sm">
                      <span className="font-medium">{space.spaceId}:</span> {space.descriptionOrLocation}
                      {space.possibleFunctions.length > 0 && (
                        <div className="ml-4 mt-1">
                          <span className="text-gray-600">Possible functions: </span>
                          {space.possibleFunctions.join(", ")}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="text-gray-600">Analysis Confidence</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Plan Clarity:</span>
                  <Badge variant="outline">
                    {analysisData.imageAnalysisConfidence.overallClarityOfPlan || "Not assessed"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Label Completeness:</span>
                  <Badge variant="outline">
                    {analysisData.imageAnalysisConfidence.completenessOfLabels || "Not assessed"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
