import { UploadSection } from "@/components/upload-section"
import { FeatureGrid } from "@/components/feature-grid"
import { HeroSection } from "@/components/hero-section"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { ArrowRight, BarChart3 } from "lucide-react"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <HeroSection />
      <UploadSection />

      <div className="container mx-auto px-4 py-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Or Try with Sample Floor Plans</h2>
        <p className="text-gray-600 mb-6">
          Test the analyzer with our collection of sample floor plans using real AI analysis
        </p>
        <div className="flex justify-center gap-4">
          <Link href="/samples">
            <Button className="bg-blue-600 hover:bg-blue-700">
              View Sample Floor Plans <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
          </Link>
          <Link href="/status">
            <Button variant="outline">
              <BarChart3 className="mr-2 w-4 h-4" />
              View Development Status
            </Button>
          </Link>
        </div>
      </div>

      <FeatureGrid />

      <div className="container mx-auto px-4 py-16 text-center bg-white/50">
        <h2 className="text-3xl font-bold mb-6">Comprehensive AI Analysis</h2>
        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          Our advanced AI system analyzes every aspect of your floor plan using refined prompts and multiple AI models
          to provide detailed insights on room layouts, Vastu compliance, accessibility, storage, circulation, and more.
        </p>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">20+</div>
            <div className="text-sm text-gray-600">Analysis Categories</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">4</div>
            <div className="text-sm text-gray-600">AI Models</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600">80%</div>
            <div className="text-sm text-gray-600">PRD Complete</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600">100%</div>
            <div className="text-sm text-gray-600">Core Features</div>
          </div>
        </div>
      </div>
    </div>
  )
}
