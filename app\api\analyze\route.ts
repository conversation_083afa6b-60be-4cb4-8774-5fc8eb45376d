import { type NextRequest, NextResponse } from "next/server"
import { analyzeFloorPlan } from "@/lib/analyze-floor-plan"

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const result = await analyzeFloorPlan(formData)

    return NextResponse.json(result)
  } catch (error) {
    console.error("API Error:", error)
    return NextResponse.json({ success: false, error: "Analysis failed" }, { status: 500 })
  }
}
