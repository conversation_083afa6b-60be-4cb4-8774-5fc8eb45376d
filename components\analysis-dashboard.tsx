"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Share2, RefreshCw } from "lucide-react"
import { AnalysisResults } from "@/components/analysis-results"
import { Floor<PERSON>lanViewer } from "@/components/floor-plan-viewer"
import { PDFExportButton } from "@/components/pdf-export-button"

interface AnalysisDashboardProps {
  initialData?: any
}

export function AnalysisDashboard({ initialData }: AnalysisDashboardProps) {
  const [analysisData] = useState(initialData)
  const [isReanalyzing, setIsReanalyzing] = useState(false)

  const handleReanalyze = () => {
    setIsReanalyzing(true)

    // Simulate re-analysis
    setTimeout(() => {
      setIsReanalyzing(false)
      alert("Re-analysis complete! This is a demo version.")
    }, 2000)
  }

  const handleShare = () => {
    try {
      // Generate shareable link
      const shareableLink = `${window.location.origin}/shared/${analysisData?.id || "demo"}`

      // Copy to clipboard
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(shareableLink)
          .then(() => {
            alert("Shareable link copied to clipboard!")
          })
          .catch(() => {
            alert("Failed to copy link. Please copy manually: " + shareableLink)
          })
      } else {
        alert("Shareable link: " + shareableLink)
      }
    } catch (error) {
      console.error("Share error:", error)
      alert("Failed to generate shareable link.")
    }
  }

  if (!analysisData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">No Analysis Data</h1>
          <p className="text-gray-600">Please upload a floor plan to get started.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold">{analysisData.projectInfo?.projectName || "Floor Plan Analysis"}</h1>
          <div className="flex flex-wrap items-center gap-2 mt-2">
            <Badge variant="outline">Analyzed by {analysisData.metadata?.providerName || "AI"}</Badge>
            <Badge variant="secondary">
              {analysisData.metadata?.timestamp
                ? new Date(analysisData.metadata.timestamp).toLocaleDateString()
                : new Date().toLocaleDateString()}
            </Badge>
            {analysisData.projectInfo?.city && <Badge variant="outline">{analysisData.projectInfo.city}</Badge>}
            {analysisData.projectInfo?.superArea && (
              <Badge variant="outline">
                {analysisData.projectInfo.superArea} {analysisData.projectInfo.superAreaUnit || "sqft"}
              </Badge>
            )}
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button variant="outline" onClick={handleReanalyze} disabled={isReanalyzing}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isReanalyzing ? "animate-spin" : ""}`} />
            Re-analyze
          </Button>
          <Button variant="outline" onClick={handleShare}>
            <Share2 className="w-4 h-4 mr-2" />
            Share
          </Button>
          <PDFExportButton analysisData={analysisData} visualizationElementId="floor-plan-canvas-container" />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <FloorPlanViewer analysisData={analysisData} />
        </div>
        <div>
          <AnalysisResults analysisData={analysisData} />
        </div>
      </div>
    </div>
  )
}
