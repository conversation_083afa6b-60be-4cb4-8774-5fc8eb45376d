"use server"

/**
 * Environment variable configuration
 */
export const environmentVariables = [
  {
    name: "OPENAI_API_KEY",
    description: "OpenAI API key",
    required: false,
    setupUrl: "https://platform.openai.com/account/api-keys",
  },
  {
    name: "ANTHROPIC_API_KEY",
    description: "Anthropic API key",
    required: false,
    setupUrl: "https://console.anthropic.com/settings/keys",
  },
  {
    name: "GOOGLE_GENERATIVE_AI_API_KEY",
    description: "Google AI API key",
    required: false,
    setupUrl: "https://aistudio.google.com/app/apikey",
  },
  {
    name: "GROQ_API_KEY",
    description: "Groq API key",
    required: false,
    setupUrl: "https://console.groq.com/keys",
  },
  {
    name: "MISTRAL_API_KEY",
    description: "Mistral AI API key",
    required: false,
    setupUrl: "https://console.mistral.ai/",
    hint: "Mistral provides high-quality multilingual AI models",
  },
  {
    name: "DEEPSEEK_API_KEY",
    description: "DeepSeek API key",
    required: false,
    setupUrl: "https://platform.deepseek.com/",
    hint: "DeepSeek offers advanced reasoning capabilities",
  },
  {
    name: "QWEN_API_KEY",
    description: "Qwen API key",
    required: false,
    setupUrl: "https://dashscope.aliyun.com/",
    hint: "Qwen (Tongyi Qianwen) by Alibaba Cloud",
  },
  {
    name: "OPENROUTER_API_KEY",
    description: "OpenRouter API key for accessing multiple models",
    required: false,
    setupUrl: "https://openrouter.ai/keys",
    hint: "OpenRouter provides access to multiple AI models with competitive pricing",
  },
]

/**
 * Utility to check if required environment variables are present
 * and provide detailed error messages
 */
export async function checkEnvironmentVariables() {
  const allVariables = [
    "GROQ_API_KEY",
    "OPENAI_API_KEY",
    "ANTHROPIC_API_KEY",
    "GOOGLE_GENERATIVE_AI_API_KEY",
    "MISTRAL_API_KEY",
    "DEEPSEEK_API_KEY",
    "QWEN_API_KEY",
    "OPENROUTER_API_KEY",
  ]

  const missingVariables: string[] = []
  const availableVariables: string[] = []

  for (const variable of allVariables) {
    const value = process.env[variable]
    if (!value || value.trim() === "") {
      missingVariables.push(variable)
    } else {
      availableVariables.push(variable)
    }
  }

  console.log("Environment check results:", {
    availableVariables,
    missingVariables,
    totalChecked: allVariables.length,
  })

  return {
    success: availableVariables.length > 0,
    missingVariables,
    availableVariables,
    totalAvailable: availableVariables.length,
  }
}

/**
 * Get available AI providers based on environment variables
 */
export function getAvailableProviders() {
  const providers = []

  if (process.env.GROQ_API_KEY?.trim()) {
    providers.push("groq")
  }

  if (process.env.OPENAI_API_KEY?.trim()) {
    providers.push("openai")
  }

  if (process.env.ANTHROPIC_API_KEY?.trim()) {
    providers.push("anthropic")
  }

  if (process.env.GOOGLE_GENERATIVE_AI_API_KEY?.trim()) {
    providers.push("google")
  }

  if (process.env.MISTRAL_API_KEY?.trim()) {
    providers.push("mistral")
  }

  if (process.env.DEEPSEEK_API_KEY?.trim()) {
    providers.push("deepseek")
  }

  if (process.env.QWEN_API_KEY?.trim()) {
    providers.push("qwen")
  }

  if (process.env.OPENROUTER_API_KEY?.trim()) {
    providers.push("openrouter")
  }

  return providers
}

/**
 * Check if a specific provider is available
 */
export function isProviderAvailable(provider: string): boolean {
  const availableProviders = getAvailableProviders()
  return availableProviders.includes(provider)
}
