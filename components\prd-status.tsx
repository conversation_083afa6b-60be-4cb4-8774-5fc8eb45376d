"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Clock, AlertCircle } from "lucide-react"

interface FeatureStatus {
  name: string
  status: "completed" | "in-progress" | "planned"
  description: string
  priority: "high" | "medium" | "low"
}

const features: FeatureStatus[] = [
  {
    name: "AI-Powered Floor Plan Analysis",
    status: "completed",
    description: "Comprehensive analysis using multiple AI models with refined prompts",
    priority: "high",
  },
  {
    name: "Multiple AI Model Support",
    status: "completed",
    description: "Support for OpenAI GPT-4, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Google Gemini",
    priority: "high",
  },
  {
    name: "Room Identification & Measurements",
    status: "completed",
    description: "Automatic detection of rooms with dimensions and area calculations",
    priority: "high",
  },
  {
    name: "Vastu Compliance Analysis",
    status: "completed",
    description: "Traditional Indian architectural principles assessment",
    priority: "high",
  },
  {
    name: "Design Flaw Detection",
    status: "completed",
    description: "Identification of design issues with improvement suggestions",
    priority: "high",
  },
  {
    name: "Natural Light & Ventilation Analysis",
    status: "completed",
    description: "Assessment of lighting and airflow patterns",
    priority: "high",
  },
  {
    name: "Space Optimization Suggestions",
    status: "completed",
    description: "Recommendations for better space utilization",
    priority: "high",
  },
  {
    name: "Interactive Floor Plan Viewer",
    status: "completed",
    description: "Zoomable, rotatable floor plan visualization with room overlays",
    priority: "medium",
  },
  {
    name: "AI Chat Interface",
    status: "completed",
    description: "Ask questions about your floor plan analysis",
    priority: "medium",
  },
  {
    name: "Real-time Streaming Analysis",
    status: "completed",
    description: "Live updates during analysis process",
    priority: "medium",
  },
  {
    name: "Sample Floor Plans Gallery",
    status: "completed",
    description: "Pre-loaded sample floor plans for testing",
    priority: "medium",
  },
  {
    name: "Model Testing & Validation",
    status: "completed",
    description: "Test AI model connections and performance",
    priority: "medium",
  },
  {
    name: "PDF Export",
    status: "completed",
    description: "Export analysis results as PDF reports",
    priority: "medium",
  },
  {
    name: "Sharing Functionality",
    status: "completed",
    description: "Generate shareable links for analysis results",
    priority: "medium",
  },
  {
    name: "Accessibility Features Analysis",
    status: "completed",
    description: "Assessment of wheelchair accessibility and mobility features",
    priority: "medium",
  },
  {
    name: "Storage & Organization Analysis",
    status: "completed",
    description: "Evaluation of storage spaces and organization potential",
    priority: "medium",
  },
  {
    name: "Circulation & Flow Analysis",
    status: "completed",
    description: "Assessment of movement patterns and traffic flow",
    priority: "medium",
  },
  {
    name: "Multi-file Upload Support",
    status: "completed",
    description: "Support for multiple floor plan images and PDF files",
    priority: "low",
  },
  {
    name: "Comprehensive Analysis Schema",
    status: "completed",
    description: "Structured JSON output with detailed analysis data",
    priority: "high",
  },
  {
    name: "Environment Variable Management",
    status: "completed",
    description: "Secure API key management and validation",
    priority: "high",
  },
  {
    name: "User Authentication",
    status: "planned",
    description: "User accounts and analysis history",
    priority: "medium",
  },
  {
    name: "Database Integration",
    status: "planned",
    description: "Persistent storage for analysis results",
    priority: "medium",
  },
  {
    name: "Analysis History",
    status: "planned",
    description: "View and manage previous analyses",
    priority: "medium",
  },
  {
    name: "Batch Processing",
    status: "planned",
    description: "Analyze multiple floor plans simultaneously",
    priority: "low",
  },
  {
    name: "Cost Tracking",
    status: "planned",
    description: "Track API usage and costs",
    priority: "low",
  },
]

export function PRDStatus() {
  const completedFeatures = features.filter((f) => f.status === "completed")
  const inProgressFeatures = features.filter((f) => f.status === "in-progress")
  const plannedFeatures = features.filter((f) => f.status === "planned")

  const completionPercentage = Math.round((completedFeatures.length / features.length) * 100)

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Product Development Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{completedFeatures.length}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-600">{inProgressFeatures.length}</div>
              <div className="text-sm text-gray-600">In Progress</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{plannedFeatures.length}</div>
              <div className="text-sm text-gray-600">Planned</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">{completionPercentage}%</div>
              <div className="text-sm text-gray-600">Complete</div>
            </div>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-green-600 h-3 rounded-full transition-all duration-300"
              style={{ width: `${completionPercentage}%` }}
            />
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-600">
              <CheckCircle className="w-5 h-5" />
              Completed Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {completedFeatures.map((feature, index) => (
                <div key={index} className="border-l-4 border-green-500 pl-3">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-sm">{feature.name}</h4>
                    <Badge variant={feature.priority === "high" ? "default" : "secondary"}>{feature.priority}</Badge>
                  </div>
                  <p className="text-xs text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-600">
              <Clock className="w-5 h-5" />
              In Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            {inProgressFeatures.length === 0 ? (
              <p className="text-gray-500 text-sm">No features currently in progress</p>
            ) : (
              <div className="space-y-3">
                {inProgressFeatures.map((feature, index) => (
                  <div key={index} className="border-l-4 border-yellow-500 pl-3">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-sm">{feature.name}</h4>
                      <Badge variant={feature.priority === "high" ? "default" : "secondary"}>{feature.priority}</Badge>
                    </div>
                    <p className="text-xs text-gray-600">{feature.description}</p>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-600">
              <AlertCircle className="w-5 h-5" />
              Planned Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {plannedFeatures.map((feature, index) => (
                <div key={index} className="border-l-4 border-blue-500 pl-3">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-sm">{feature.name}</h4>
                    <Badge variant={feature.priority === "high" ? "default" : "secondary"}>{feature.priority}</Badge>
                  </div>
                  <p className="text-xs text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
