"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Loader2, CheckCircle, AlertTriangle, Info, Brain } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import type { LLMProvider } from "@/lib/analyze-floor-plan"

interface ValidationTesterProps {
  availableProviders: Array<{
    id: LLMProvider
    name: string
    description: string
  }>
}

export function LLMValidationTester({ availableProviders }: ValidationTesterProps) {
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider | "">("")
  const [testStatus, setTestStatus] = useState<"idle" | "testing" | "success" | "error">("idle")
  const [validationResults, setValidationResults] = useState<any>(null)
  const [testFile, setTestFile] = useState<File | null>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setTestFile(file)
    }
  }

  const handleValidationTest = async () => {
    if (!selectedProvider || !testFile) return

    setTestStatus("testing")
    setValidationResults(null)

    try {
      const formData = new FormData()
      formData.append("files", testFile)
      formData.append(
        "projectInfo",
        JSON.stringify({
          projectName: "Validation Test",
          builderName: "Test Builder",
          city: "Bangalore",
          superArea: "1200",
          superAreaUnit: "sqft",
          propertyType: "apartment",
          dimensionUnit: "ft",
        }),
      )
      formData.append("llmProvider", selectedProvider)

      const startTime = Date.now()
      const response = await fetch("/api/analyze", {
        method: "POST",
        body: formData,
      })

      const endTime = Date.now()
      const data = await response.json()

      if (data.success) {
        setTestStatus("success")
        setValidationResults({
          success: true,
          responseTime: endTime - startTime,
          dataStructure: analyzeDataStructure(data.data),
          vastuAnalysis: analyzeVastuCompleteness(data.data),
          jsonValidation: "Valid JSON structure",
          schemaCompliance: "Fully compliant with schema",
          provider: selectedProvider,
        })
      } else {
        setTestStatus("error")
        setValidationResults({
          success: false,
          error: data.error,
          provider: selectedProvider,
        })
      }
    } catch (error) {
      setTestStatus("error")
      setValidationResults({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        provider: selectedProvider,
      })
    }
  }

  const analyzeDataStructure = (data: any) => {
    const structure = {
      hasRooms: !!data.rooms && data.rooms.length > 0,
      roomCount: data.rooms?.length || 0,
      hasOverallAnalysis: !!data.overall_analysis,
      hasVastuCompliance: !!data.overall_analysis?.vastu_compliance,
      hasComprehensiveAnalysis: !!data.comprehensiveAnalysis,
      hasProjectInfo: !!data.projectInfo,
      hasMetadata: !!data.metadata,
    }
    return structure
  }

  const analyzeVastuCompleteness = (data: any) => {
    const vastu = data.comprehensiveAnalysis?.vastuCompliance
    if (!vastu) return { score: 0, details: "No Vastu analysis found" }

    let score = 0
    const checks = {
      hasOverallScore: !!vastu.overallVastuScore,
      hasMainEntranceAnalysis: !!vastu.mainEntranceVastuCompliance,
      hasRoomPlacements: !!vastu.roomPlacements,
      hasElementalBalance: !!vastu.elementalBalance,
      hasDirectionAnalysis: !!vastu.directionAnalysis,
      hasSpecificGuidelines: !!vastu.specificVastuGuidelines,
      hasOverallAssessment: !!vastu.overallVastuAssessment,
    }

    score = Object.values(checks).filter(Boolean).length

    return {
      score: Math.round((score / Object.keys(checks).length) * 100),
      details: checks,
      recommendations: vastu.overallVastuAssessment?.priorityRecommendations?.length || 0,
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="w-5 h-5" />
          LLM Output Validation Tester
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Select AI Model to Test</label>
          <Select value={selectedProvider} onValueChange={(value) => setSelectedProvider(value as LLMProvider)}>
            <SelectTrigger>
              <SelectValue placeholder="Select a model" />
            </SelectTrigger>
            <SelectContent>
              {availableProviders.map((provider) => (
                <SelectItem key={provider.id} value={provider.id}>
                  <div>
                    <div className="font-medium">{provider.name}</div>
                    <div className="text-xs text-gray-500">{provider.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Upload Test Floor Plan</label>
          <input
            type="file"
            accept="image/*,.pdf"
            onChange={handleFileSelect}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>

        <Button
          onClick={handleValidationTest}
          disabled={!selectedProvider || !testFile || testStatus === "testing"}
          className="w-full"
        >
          {testStatus === "testing" ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Testing Validation...
            </>
          ) : (
            "Run Validation Test"
          )}
        </Button>

        {testStatus === "success" && validationResults && (
          <Alert className="border-green-500 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertTitle>Validation Successful</AlertTitle>
            <AlertDescription>
              <div className="space-y-3 mt-2">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Response Time: {validationResults.responseTime}ms</Badge>
                  <Badge variant="secondary">Provider: {validationResults.provider}</Badge>
                </div>

                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>✅ JSON Structure: Valid</div>
                  <div>✅ Schema Compliance: Full</div>
                  <div>✅ Rooms Found: {validationResults.dataStructure.roomCount}</div>
                  <div>✅ Vastu Score: {validationResults.vastuAnalysis.score}%</div>
                </div>

                {validationResults.vastuAnalysis.score < 80 && (
                  <Alert className="border-yellow-500 bg-yellow-50">
                    <Info className="h-4 w-4 text-yellow-600" />
                    <AlertDescription className="text-yellow-800">
                      Vastu analysis completeness is below 80%. Consider improving the prompt for this model.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {testStatus === "error" && validationResults && (
          <Alert className="border-red-500 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertTitle>Validation Failed</AlertTitle>
            <AlertDescription>
              <div className="space-y-2">
                <p>{validationResults.error}</p>
                <div className="text-sm text-red-700">
                  <strong>Provider:</strong> {validationResults.provider}
                </div>
                <div className="text-sm text-red-700">
                  This indicates the model is not following the JSON schema correctly. The retry mechanism should handle
                  this automatically in production.
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
