import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Home, Zap, Shield } from "lucide-react"

export function HeroSection() {
  return (
    <section className="container mx-auto px-4 py-16 text-center">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-5xl font-bold text-gray-900 mb-6">AI-Powered Floor Plan Analyzer</h1>
        <p className="text-xl text-gray-600 mb-8 leading-relaxed">
          Transform complex floor plans into clear, actionable insights. Get architect-level analysis of carpet areas,
          design flaws, Vastu compliance, and space optimization to make confident property decisions.
        </p>
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          <div className="flex items-center gap-2 bg-white px-4 py-2 rounded-full shadow-sm">
            <Home className="w-5 h-5 text-blue-600" />
            <span className="text-sm font-medium">Room Analysis</span>
          </div>
          <div className="flex items-center gap-2 bg-white px-4 py-2 rounded-full shadow-sm">
            <Zap className="w-5 h-5 text-green-600" />
            <span className="text-sm font-medium">Instant Results</span>
          </div>
          <div className="flex items-center gap-2 bg-white px-4 py-2 rounded-full shadow-sm">
            <Shield className="w-5 h-5 text-purple-600" />
            <span className="text-sm font-medium">Vastu Compliance</span>
          </div>
        </div>
        <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
          Start Analysis <ArrowRight className="ml-2 w-5 h-5" />
        </Button>
      </div>
    </section>
  )
}
