"use server"

import { generateObject, generateText } from "ai"
import { openai } from "@ai-sdk/openai"
import { anthropic } from "@ai-sdk/anthropic"
import { google } from "@ai-sdk/google"
import { groq } from "@ai-sdk/groq"
import { mistral } from "@ai-sdk/mistral"
import { comprehensiveAnalysisSchema, type ComprehensiveAnalysisResult } from "@/lib/analysis-schema"
import { generateContextualPrompt } from "@/lib/analysis-prompt"
import { getSupabaseClient } from "./supabaseClient"; // New import for potentially using service role

// Helper to map provider slugs to AI SDK constructors
const sdkConstructors: Record<string, any> = {
  openai: openaiSdk,
  anthropic: anthropicSdk,
  google: googleSdk,
  groq: groqSdk,
  mistral: mistralSdk,
  // For providers like DeepSeek, Qwen, OpenRouter that use OpenAI-compatible APIs,
  // we can use the openaiSdk with a custom baseURL.
};

// Helper function to create a model instance dynamically
async function getModelInstance(
  modelId: string
): Promise<LanguageModel | null> {
  const supabase = getSupabaseClient(true); // Use service role client
  console.log(`Fetching configuration for modelId: ${modelId} using appropriate Supabase client.`);
  const { data: modelData, error: modelError } = await supabase
    .from('Models')
    .select(`
      id,
      model_identifier,
      description,
      is_active,
      ModelProviders ( provider_slug, api_base_url, is_active ),
      ProviderAPIKeys ( api_key, is_active, last_used_at, use_count )
    `)
    .eq('id', modelId)
    .eq('is_active', true)
    .single();

  if (modelError || !modelData) {
    console.error(`Error fetching model ${modelId}:`, modelError);
    return null;
  }

  if (!modelData.ModelProviders || !modelData.ModelProviders.is_active) {
    console.error(`Provider for model ${modelId} is not active or not found.`);
    return null;
  }

  const activeKeys = modelData.ProviderAPIKeys?.filter(key => key.is_active);
  if (!activeKeys || activeKeys.length === 0) {
    console.error(`No active API keys found for model ${modelId}.`);
    return null;
  }
  
  // Simple strategy: use the first active key. Could be round-robin or least recently used.
  const apiKeyToUse = activeKeys[0].api_key;
  // TODO: Implement API key rotation/selection strategy and update use_count/last_used_at

  const providerSlug = modelData.ModelProviders.provider_slug;
  const modelIdentifier = modelData.model_identifier;
  const baseURL = modelData.ModelProviders.api_base_url;

  const constructor = sdkConstructors[providerSlug];

  const options: { apiKey?: string; baseURL?: string } = {};
  if (apiKeyToUse) options.apiKey = apiKeyToUse;
  if (baseURL) options.baseURL = baseURL;

  if (constructor) {
    return constructor(modelIdentifier, options);
  } else if (baseURL) {
    // Fallback for OpenAI-compatible APIs not explicitly in sdkConstructors
    console.log(`Using OpenAI SDK for ${providerSlug} with baseURL: ${baseURL}`);
    return openaiSdk(modelIdentifier, options);
  }

  console.error(`Unsupported provider slug or configuration for model ${modelId}: ${providerSlug}`);
  return null;
}

// Represents a model ID from the Supabase 'Models' table
export type LLMProvider = string; 

export interface AvailableModel {
  id: string; // Model ID from Supabase 'Models' table
  name: string; // Model name (e.g., GPT-4o, Claude 3.5 Sonnet)
  providerName: string; // Provider name (e.g., OpenAI, Anthropic)
  description: string | null; // Model description
  available: boolean;
}

export async function getAvailableProviders(): Promise<AvailableModel[]> {
  const supabase = getSupabaseClient(true); // Use service role client
  console.log("Fetching available models from Supabase using appropriate client...");
  try {
    const { data: models, error } = await supabase
      .from('Models')
      .select(`
        id,
        name,
        description,
        is_active,
        ModelProviders!inner ( name, provider_slug, is_active ),
        ProviderAPIKeys ( id, is_active )
      `)
      .eq('is_active', true)
      .eq('ModelProviders.is_active', true);

    if (error) {
      console.error("Error fetching models from Supabase:", error);
      return [];
    }

    if (!models) {
      console.log("No models found in Supabase.");
      return [];
    }

    const availableModels: AvailableModel[] = models.map(model => {
      const hasActiveKeys = model.ProviderAPIKeys ? model.ProviderAPIKeys.some(key => key.is_active) : false;
      const isModelAvailable = model.is_active && model.ModelProviders.is_active && hasActiveKeys;
      
      if(isModelAvailable){
        console.log(`✅ Model ${model.name} (Provider: ${model.ModelProviders.name}) available.`);
      } else {
        console.log(`❌ Model ${model.name} (Provider: ${model.ModelProviders.name}) not available. Active: ${model.is_active}, Provider Active: ${model.ModelProviders.is_active}, Has Active Keys: ${hasActiveKeys}`);
      }

      return {
        id: model.id,
        name: model.name,
        providerName: model.ModelProviders.name,
        description: model.description,
        available: isModelAvailable,
      };
    }).filter(model => model.available);

    console.log(`Total available models from Supabase: ${availableModels.length}`);
    return availableModels;

  } catch (err) {
    console.error("Unexpected error in getAvailableProviders:", err);
    return [];
  }
}

/**
 * Validates and parses JSON response, handling common formatting issues
 */
function validateAndParseJSON(text: string): any {
  try {
    // First, try direct parsing
    return JSON.parse(text)
  } catch (error) {
    console.log("Direct JSON parse failed, attempting cleanup...")

    // Remove markdown code blocks
    let cleaned = text.replace(/```json\s*/g, "").replace(/```\s*/g, "")

    // Remove any leading/trailing whitespace
    cleaned = cleaned.trim()

    // Try to find JSON object boundaries
    const jsonStart = cleaned.indexOf("{")
    const jsonEnd = cleaned.lastIndexOf("}")

    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      cleaned = cleaned.substring(jsonStart, jsonEnd + 1)
    }

    try {
      return JSON.parse(cleaned)
    } catch (secondError) {
      console.error("JSON cleanup failed:", secondError)
      throw new Error(`Invalid JSON response: ${secondError}`)
    }
  }
}

/**
 * Retry mechanism with exponential backoff
 */
async function analyzeWithRetry(model: any, messages: any[], maxRetries = 3): Promise<ComprehensiveAnalysisResult> {
  let lastError: Error | null = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Analysis attempt ${attempt}/${maxRetries}`)

      // Try structured generation first
      try {
        const result = await generateObject({
          model,
          schema: comprehensiveAnalysisSchema,
          messages,
        })

        console.log("Structured generation successful")
        return result.object
      } catch (structuredError) {
        console.log("Structured generation failed, trying text generation...")

        // Fallback to text generation with manual parsing
        const textResult = await generateText({
          model,
          messages: [
            ...messages,
            {
              role: "system",
              content: "IMPORTANT: Respond ONLY with valid JSON. No markdown, no explanations, just the JSON object.",
            },
          ],
        })

        const parsedJSON = validateAndParseJSON(textResult.text)
        const validatedResult = comprehensiveAnalysisSchema.parse(parsedJSON)

        console.log("Text generation with manual parsing successful")
        return validatedResult
      }
    } catch (error) {
      lastError = error as Error
      console.error(`Attempt ${attempt} failed:`, error)

      if (attempt < maxRetries) {
        // Exponential backoff: 1s, 2s, 4s
        const delay = Math.pow(2, attempt - 1) * 1000
        console.log(`Waiting ${delay}ms before retry...`)
        await new Promise((resolve) => setTimeout(resolve, delay))
      }
    }
  }

  throw lastError || new Error("All retry attempts failed")
}

/**
 * Transform the comprehensive analysis result to the legacy format for backward compatibility
 */
function transformToLegacyFormat(comprehensiveResult: ComprehensiveAnalysisResult, projectInfo: any): any {
  // Ensure projectInfo has default values
  const safeProjectInfo = {
    projectName: "Floor Plan Analysis",
    builderName: "Unknown Builder",
    city: "Unknown City",
    superArea: "1200",
    superAreaUnit: "sqft",
    propertyType: "apartment",
    dimensionUnit: "ft",
    ...projectInfo, // Override with actual values if provided
  }

  // Convert the new comprehensive format to the legacy format for existing UI components
  const legacyRooms = comprehensiveResult.rooms.map((room, index) => ({
    id: room.roomId,
    name: room.roomNameLabel || `Room ${index + 1}`,
    type: mapPrimaryFunctionToLegacyType(room.primaryFunction),
    dimensions: {
      length: room.dimensions.length || 12,
      width: room.dimensions.width || 10,
      unit: room.dimensions.unit || "ft",
      area_sqft: room.dimensions.length && room.dimensions.width ? room.dimensions.length * room.dimensions.width : 120,
      area_sqm:
        room.dimensions.length && room.dimensions.width
          ? room.dimensions.length * room.dimensions.width * 0.092903
          : 11.15,
    },
    features: room.furnitureFixtures || [],
    notes: room.specificObservations || "Standard room configuration",
    confidence_score: 0.85,
  }))

  const totalCarpetAreaSqft = legacyRooms.reduce((sum, room) => sum + room.dimensions.area_sqft, 0)

  // Enhanced Vastu compliance with detailed analysis
  const vastuCompliance = comprehensiveResult.vastuCompliance || {}

  return {
    rooms: legacyRooms,
    overall_analysis: {
      total_carpet_area_sqft: totalCarpetAreaSqft,
      total_carpet_area_sqm: totalCarpetAreaSqft * 0.092903,
      loading_percentage: safeProjectInfo.superArea
        ? ((Number.parseFloat(safeProjectInfo.superArea) - totalCarpetAreaSqft) /
            Number.parseFloat(safeProjectInfo.superArea)) *
          100
        : null,
      vastu_compliance: {
        score: vastuCompliance.overallVastuScore || 7,
        observations: [
          ...(vastuCompliance.overallVastuAssessment?.strengths || []),
          ...(vastuCompliance.mainEntranceVastuCompliance?.issues || []),
        ].slice(0, 5),
        recommendations: [
          ...(vastuCompliance.overallVastuAssessment?.priorityRecommendations || []),
          ...(vastuCompliance.mainEntranceVastuCompliance?.recommendations || []),
        ].slice(0, 8),
        detailedAnalysis: {
          mainEntrance: vastuCompliance.mainEntranceVastuCompliance || null,
          roomPlacements: vastuCompliance.roomPlacements || null,
          elementalBalance: vastuCompliance.elementalBalance || null,
          directionAnalysis: vastuCompliance.directionAnalysis || null,
          overallAssessment: vastuCompliance.overallVastuAssessment || null,
        },
      },
      design_flaws: comprehensiveResult.potentialIssuesAndImprovements.map((item) => ({
        issue: item.issue,
        severity: "medium" as const,
        location: "General",
        suggestion: item.suggestion || "Consider professional consultation",
      })),
      space_optimization: {
        score:
          comprehensiveResult.circulation.flowEfficiency === "High"
            ? 9
            : comprehensiveResult.circulation.flowEfficiency === "Medium"
              ? 7
              : 5,
        assessment: comprehensiveResult.overallAnalysis.generalImpression || "Good space utilization",
        suggestions: comprehensiveResult.positiveAspects.slice(0, 3),
      },
      natural_light: {
        score:
          comprehensiveResult.naturalLightAndVentilation.crossVentilationPotential === "Good"
            ? 9
            : comprehensiveResult.naturalLightAndVentilation.crossVentilationPotential === "Fair"
              ? 7
              : 5,
        assessment: `Window distribution is ${comprehensiveResult.naturalLightAndVentilation.overallWindowDistribution?.toLowerCase() || "adequate"}`,
        recommendations: ["Maximize natural light usage", "Consider light-colored interiors"],
      },
    },
    features: [],
    extracted_text: [],
    // Include the comprehensive analysis as additional data
    comprehensiveAnalysis: comprehensiveResult,
    // Ensure projectInfo is always included
    projectInfo: safeProjectInfo,
  }
}

function mapPrimaryFunctionToLegacyType(primaryFunction: string | null): string {
  if (!primaryFunction) return "custom_area"

  const functionMap: { [key: string]: string } = {
    Living: "living_room",
    Sleeping: "bedroom",
    Cooking: "kitchen",
    Bathing: "bathroom",
    Circulation: "corridor",
    Storage: "store_room",
    Dining: "dining_room",
    Working: "study_room",
    Utility: "utility_area",
  }

  return functionMap[primaryFunction] || "custom_area"
}

export async function analyzeFloorPlan(formData: FormData) {
  try {
    console.log("Starting analyzeFloorPlan...")

    const files = formData.getAll("files") as File[]
    const projectInfoString = formData.get("projectInfo") as string
    const selectedProvider = (formData.get("llmProvider") as LLMProvider) || "groq"

    console.log("Received data:", {
      fileCount: files.length,
      projectInfoString: projectInfoString?.substring(0, 100),
      selectedProvider,
    })

    if (files.length === 0) {
      throw new Error("No files provided")
    }

    // Parse and validate project info
    let projectInfo: any
    try {
      projectInfo = JSON.parse(projectInfoString)
      console.log("Parsed projectInfo:", projectInfo)
    } catch (error) {
      console.error("Failed to parse projectInfo:", error)
      throw new Error("Invalid project information format")
    }

    // Check if the selected provider's environment variable is available
    const providerConfig = llmProviders[selectedProvider]

    if (!providerConfig) {
      throw new Error(`Unsupported provider: ${selectedProvider}`)
    }

    // Direct check for environment variable
    const envValue = process.env[providerConfig.envKey]
    if (!envValue || envValue.trim() === "") {
      throw new Error(
        `API key for ${providerConfig.name} (${providerConfig.envKey}) is not available. Please check your environment variables.`,
      )
    }

    console.log(`✅ Using provider: ${providerConfig.name} with key: ${providerConfig.envKey}`)

    // Convert first file to base64
    const file = files[0]
    console.log("Processing file:", { name: file.name, size: file.size, type: file.type })

    const bytes = await file.arrayBuffer()
    const base64 = Buffer.from(bytes).toString("base64")

    // Generate the contextual prompt with property information
    const prompt = generateContextualPrompt(projectInfo)

    console.log("Generated prompt length:", prompt.length)

    // Use enhanced analysis with retry mechanism
    console.log("Calling AI model:", providerConfig.name)

    const messages = [
      {
        role: "user" as const,
        content: [
          { type: "text" as const, text: prompt },
          {
            type: "image" as const,
            image: `data:${file.type};base64,${base64}`,
          },
        ],
      },
    ]

    const comprehensiveAnalysis = await analyzeWithRetry(providerConfig.model, messages)

    console.log("AI analysis completed successfully")

    // Transform to legacy format for backward compatibility
    const legacyAnalysisData = transformToLegacyFormat(comprehensiveAnalysis, projectInfo)

    // Add metadata
    const enhancedData = {
      ...legacyAnalysisData,
      metadata: {
        provider: selectedProvider,
        providerName: providerConfig.name,
        timestamp: new Date().toISOString(),
        fileInfo: {
          name: file.name,
          size: file.size,
          type: file.type,
        },
        analysisVersion: "2.0", // Indicates use of comprehensive analysis
      },
    }

    console.log("Analysis completed successfully:", {
      hasRooms: !!enhancedData.rooms,
      roomCount: enhancedData.rooms?.length || 0,
      hasProjectInfo: !!enhancedData.projectInfo,
      hasOverallAnalysis: !!enhancedData.overall_analysis,
    })

    return {
      success: true,
      data: enhancedData,
      projectInfo: enhancedData.projectInfo, // Ensure projectInfo is at top level too
    }
  } catch (error) {
    console.error("Analysis error:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Analysis failed",
      details: error instanceof Error ? error.stack : undefined,
    }
  }
}

export type LegacyFormat = any
export type VastuComplianceData = any

export type { ComprehensiveAnalysisResult }
