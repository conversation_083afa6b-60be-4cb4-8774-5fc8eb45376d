import { NextResponse } from "next/server";
import { getAvailableProviders, AvailableModel } from "@/lib/analyze-floor-plan"; // Updated import

export async function GET() {
  console.log("API /api/check-env called");
  try {
    const availableModels: AvailableModel[] = await getAvailableProviders();

    const allModelsStatus = availableModels.map(model => ({
      id: model.id,
      name: model.name,
      providerName: model.providerName,
      description: model.description,
      isAvailable: model.available,
    }));

    const successfullyLoadedModels = availableModels.filter(model => model.available);
    const notAvailableModels = availableModels.filter(model => !model.available);

    console.log("Environment check based on Supabase models:", {
      successfullyLoadedModels,
      notAvailableModels,
      totalAvailable: successfullyLoadedModels.length,
    });

    return NextResponse.json({
      success: successfullyLoadedModels.length > 0,
      message: successfullyLoadedModels.length > 0 
        ? "Successfully checked model availability from database."
        : "No models are currently available based on database configuration and API key status.",
      availableModels: successfullyLoadedModels.map(m => ({ id: m.id, name: m.name, provider: m.providerName, description: m.description })),
      unavailableModels: notAvailableModels.map(m => ({ id: m.id, name: m.name, provider: m.providerName, reason: "Check database: model inactive, provider inactive, or no active API keys"})),
      totalAvailable: successfullyLoadedModels.length,
      allModelsStatus, // For more detailed debugging if needed on the client
    });

  } catch (error) {
    console.error("Environment check error (Supabase driven):", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error during environment check.",
        availableModels: [],
        unavailableModels: [],
        totalAvailable: 0,
      },
      { status: 500 },
    );
  }
}
