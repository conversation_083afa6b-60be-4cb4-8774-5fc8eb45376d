"use client"

import { jsPDF } from "jspdf"
import "jspdf-autotable"
import { toPng } from "html-to-image"

export async function generatePDF(analysisData: any, elementId: string) {
  try {
    // Create a new PDF document
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    })

    // Add title
    doc.setFontSize(20)
    doc.text("Floor Plan Analysis Report", 105, 15, { align: "center" })

    // Add project info
    doc.setFontSize(12)
    doc.text(`Project: ${analysisData.projectInfo.projectName || "N/A"}`, 20, 30)
    doc.text(`Builder: ${analysisData.projectInfo.builderName || "N/A"}`, 20, 37)
    doc.text(`City: ${analysisData.projectInfo.city || "N/A"}`, 20, 44)
    doc.text(
      `Super Area: ${analysisData.projectInfo.superArea || "N/A"} ${analysisData.projectInfo.superAreaUnit || ""}`,
      20,
      51,
    )
    doc.text(`Analysis Date: ${new Date(analysisData.metadata.timestamp).toLocaleDateString()}`, 20, 58)

    // Add overall metrics
    doc.setFontSize(16)
    doc.text("Overall Metrics", 20, 70)

    doc.setFontSize(12)
    doc.text(
      `Total Carpet Area: ${analysisData.overall_analysis.total_carpet_area_sqft} sq ft (${analysisData.overall_analysis.total_carpet_area_sqm} sq m)`,
      20,
      80,
    )
    doc.text(`Loading: ${analysisData.overall_analysis.loading_percentage?.toFixed(1) || "N/A"}%`, 20, 87)
    doc.text(`Vastu Compliance Score: ${analysisData.overall_analysis.vastu_compliance.score}/10`, 20, 94)
    doc.text(`Space Optimization Score: ${analysisData.overall_analysis.space_optimization.score}/10`, 20, 101)
    doc.text(`Natural Light Score: ${analysisData.overall_analysis.natural_light.score}/10`, 20, 108)

    // Add room details table
    doc.setFontSize(16)
    doc.text("Room Details", 20, 125)

    // @ts-ignore
    doc.autoTable({
      startY: 130,
      head: [["Room", "Type", "Dimensions", "Area (sq ft)", "Features"]],
      body: analysisData.rooms.map((room: any) => [
        room.name,
        room.type.replace("_", " "),
        `${room.dimensions.length}' × ${room.dimensions.width}'`,
        room.dimensions.area_sqft,
        room.features.join(", "),
      ]),
      theme: "grid",
    })

    // Add design flaws
    const tableEndY = (doc as any).lastAutoTable.finalY + 10
    doc.setFontSize(16)
    doc.text("Design Flaws & Recommendations", 20, tableEndY)

    // @ts-ignore
    doc.autoTable({
      startY: tableEndY + 5,
      head: [["Issue", "Severity", "Location", "Suggestion"]],
      body: analysisData.overall_analysis.design_flaws.map((flaw: any) => [
        flaw.issue,
        flaw.severity,
        flaw.location,
        flaw.suggestion,
      ]),
      theme: "grid",
    })

    // Try to capture the floor plan visualization
    if (elementId) {
      try {
        const element = document.getElementById(elementId)
        if (element) {
          const dataUrl = await toPng(element, { quality: 0.95 })

          // Add a new page for the floor plan
          doc.addPage()
          doc.setFontSize(16)
          doc.text("Floor Plan Visualization", 105, 15, { align: "center" })

          // Add the image
          doc.addImage(dataUrl, "PNG", 20, 25, 170, 170)
        }
      } catch (err) {
        console.error("Failed to capture floor plan visualization:", err)
      }
    }

    // Save the PDF
    doc.save(`floor-plan-analysis-${analysisData.projectInfo.projectName || "report"}.pdf`)

    return true
  } catch (error) {
    console.error("Failed to generate PDF:", error)
    return false
  }
}
