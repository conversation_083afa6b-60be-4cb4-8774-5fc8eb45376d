"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CheckCircle, AlertTriangle, Clock, Brain, Zap, Star } from "lucide-react"
import type { LLMProvider } from "@/lib/analyze-floor-plan"

interface TestResult {
  provider: string
  success: boolean
  responseTime: number
  vastuScore?: number
  roomCount?: number
  error?: string
  hasDetailedVastu?: boolean
}

export default function EnhancedTestPage() {
  const [availableProviders, setAvailableProviders] = useState<
    Array<{
      id: LL<PERSON><PERSON>ider
      name: string
      description: string
    }>
  >([])
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider | "">("")
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunningTests, setIsRunningTests] = useState(false)
  const [currentTest, setCurrentTest] = useState<string>("")

  useEffect(() => {
    fetchAvailableProviders()
  }, [])

  const fetchAvailableProviders = async () => {
    try {
      const response = await fetch("/api/check-env")
      const data = await response.json()

      // Mock providers based on available env vars
      const providers = []

      if (data.availableVariables.includes("GROQ_API_KEY")) {
        providers.push({ id: "groq", name: "Groq Llama Vision", description: "Ultra-fast processing" })
      }
      if (data.availableVariables.includes("OPENAI_API_KEY")) {
        providers.push(
          { id: "openai", name: "OpenAI GPT-4o", description: "Highly accurate analysis" },
          { id: "openai-gpt4-1", name: "OpenAI GPT-4.1", description: "Latest OpenAI model" },
          { id: "openai-gpt4-1-mini", name: "OpenAI GPT-4.1 Mini", description: "Faster GPT-4.1" },
        )
      }
      if (data.availableVariables.includes("ANTHROPIC_API_KEY")) {
        providers.push({ id: "anthropic", name: "Claude 3.5 Sonnet", description: "Excellent architectural analysis" })
      }
      if (data.availableVariables.includes("GOOGLE_GENERATIVE_AI_API_KEY")) {
        providers.push(
          { id: "gemini", name: "Gemini 1.5 Flash", description: "Fast and efficient" },
          { id: "gemini-2-5", name: "Gemini 2.5 Flash", description: "Enhanced vision capabilities" },
          { id: "gemini-2-5-pro", name: "Gemini 2.5 Pro", description: "Most advanced Google model" },
        )
      }
      if (data.availableVariables.includes("MISTRAL_API_KEY")) {
        providers.push(
          { id: "mistral", name: "Mistral Large", description: "High-quality multilingual analysis" },
          { id: "mistral-small", name: "Mistral Small", description: "Fast and efficient Mistral model" },
        )
      }
      if (data.availableVariables.includes("DEEPSEEK_API_KEY")) {
        providers.push({ id: "deepseek", name: "DeepSeek V3", description: "Advanced reasoning capabilities" })
      }
      if (data.availableVariables.includes("QWEN_API_KEY")) {
        providers.push({ id: "qwen", name: "Qwen 2.5 VL", description: "Strong spatial understanding" })
      }
      if (data.availableVariables.includes("OPENROUTER_API_KEY")) {
        providers.push(
          { id: "openrouter-gpt4o", name: "OpenRouter GPT-4o", description: "GPT-4o via OpenRouter" },
          { id: "openrouter-claude", name: "OpenRouter Claude", description: "Claude via OpenRouter" },
          { id: "openrouter-gemini", name: "OpenRouter Gemini", description: "Gemini via OpenRouter" },
          { id: "openrouter-qwen", name: "OpenRouter Qwen VL", description: "Qwen VL via OpenRouter" },
        )
      }

      setAvailableProviders(providers as any)
    } catch (error) {
      console.error("Failed to fetch providers:", error)
    }
  }

  const runSingleTest = async (provider: LLMProvider) => {
    setCurrentTest(provider)

    // Create a test floor plan (simple rectangle)
    const canvas = document.createElement("canvas")
    canvas.width = 800
    canvas.height = 600
    const ctx = canvas.getContext("2d")!

    // Draw a simple floor plan
    ctx.fillStyle = "white"
    ctx.fillRect(0, 0, 800, 600)
    ctx.strokeStyle = "black"
    ctx.lineWidth = 2

    // Outer walls
    ctx.strokeRect(50, 50, 700, 500)

    // Rooms
    ctx.strokeRect(50, 50, 350, 250) // Living room
    ctx.strokeRect(400, 50, 350, 250) // Kitchen
    ctx.strokeRect(50, 300, 350, 250) // Bedroom
    ctx.strokeRect(400, 300, 350, 250) // Bathroom

    // Labels
    ctx.font = "16px Arial"
    ctx.fillStyle = "black"
    ctx.fillText("Living Room", 150, 150)
    ctx.fillText("Kitchen", 500, 150)
    ctx.fillText("Bedroom", 150, 400)
    ctx.fillText("Bathroom", 500, 400)

    // Convert to blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => resolve(blob!), "image/png")
    })

    const file = new File([blob], "test-floor-plan.png", { type: "image/png" })

    const formData = new FormData()
    formData.append("files", file)
    formData.append(
      "projectInfo",
      JSON.stringify({
        projectName: "Enhanced Test",
        builderName: "Test Builder",
        city: "Mumbai",
        superArea: "1000",
        superAreaUnit: "sqft",
        propertyType: "apartment",
        dimensionUnit: "ft",
      }),
    )
    formData.append("llmProvider", provider)

    const startTime = Date.now()

    try {
      const response = await fetch("/api/analyze", {
        method: "POST",
        body: formData,
      })

      const endTime = Date.now()
      const data = await response.json()

      if (data.success) {
        const vastuAnalysis = data.data.comprehensiveAnalysis?.vastuCompliance

        return {
          provider,
          success: true,
          responseTime: endTime - startTime,
          vastuScore: vastuAnalysis?.overallVastuScore || 0,
          roomCount: data.data.rooms?.length || 0,
          hasDetailedVastu: !!(vastuAnalysis?.roomPlacements && vastuAnalysis?.directionAnalysis),
        }
      } else {
        return {
          provider,
          success: false,
          responseTime: endTime - startTime,
          error: data.error,
        }
      }
    } catch (error) {
      return {
        provider,
        success: false,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  const runAllTests = async () => {
    setIsRunningTests(true)
    setTestResults([])

    for (const provider of availableProviders) {
      const result = await runSingleTest(provider.id)
      setTestResults((prev) => [...prev, result])
    }

    setIsRunningTests(false)
    setCurrentTest("")
  }

  const runSingleProviderTest = async () => {
    if (!selectedProvider) return

    setIsRunningTests(true)
    setTestResults([])

    const result = await runSingleProviderTest()
    setTestResults([result])

    setIsRunningTests(false)
  }

  const getProviderStatus = (provider: string) => {
    const result = testResults.find((r) => r.provider === provider)
    if (!result) return "pending"
    return result.success ? "success" : "error"
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case "error":
        return <AlertTriangle className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Enhanced Floor Plan Analyzer Test Suite</h1>
        <p className="text-gray-600">Test the new validation, Vastu analysis, and model integrations</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Brain className="w-5 h-5" />
              Available Models
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{availableProviders.length}</div>
            <p className="text-sm text-gray-600">AI models configured</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Zap className="w-5 h-5" />
              Success Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {testResults.length > 0
                ? `${Math.round((testResults.filter((r) => r.success).length / testResults.length) * 100)}%`
                : "0%"}
            </div>
            <p className="text-sm text-gray-600">Tests passed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Star className="w-5 h-5" />
              Avg Vastu Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {testResults.length > 0
                ? `${Math.round(testResults.filter((r) => r.vastuScore).reduce((sum, r) => sum + (r.vastuScore || 0), 0) / testResults.filter((r) => r.vastuScore).length)}/10`
                : "N/A"}
            </div>
            <p className="text-sm text-gray-600">Vastu compliance</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="test" className="space-y-4">
        <TabsList>
          <TabsTrigger value="test">Run Tests</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        <TabsContent value="test" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Test Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <div className="flex-1">
                  <label className="text-sm font-medium">Select Model to Test</label>
                  <Select value={selectedProvider} onValueChange={(value) => setSelectedProvider(value as LLMProvider)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableProviders.map((provider) => (
                        <SelectItem key={provider.id} value={provider.id}>
                          <div>
                            <div className="font-medium">{provider.name}</div>
                            <div className="text-xs text-gray-500">{provider.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={runSingleProviderTest}
                  disabled={!selectedProvider || isRunningTests}
                  variant="outline"
                >
                  Test Selected Model
                </Button>
                <Button onClick={runAllTests} disabled={isRunningTests || availableProviders.length === 0}>
                  Test All Models
                </Button>
              </div>

              {isRunningTests && (
                <Alert>
                  <Clock className="h-4 w-4" />
                  <AlertTitle>Running Tests...</AlertTitle>
                  <AlertDescription>
                    {currentTest ? `Currently testing: ${currentTest}` : "Preparing tests..."}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {testResults.length > 0 ? (
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        {getStatusIcon(result.success ? "success" : "error")}
                        {result.provider}
                      </span>
                      <Badge variant={result.success ? "default" : "destructive"}>
                        {result.success ? "Success" : "Failed"}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {result.success ? (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <div className="font-medium">Response Time</div>
                          <div>{result.responseTime}ms</div>
                        </div>
                        <div>
                          <div className="font-medium">Rooms Found</div>
                          <div>{result.roomCount}</div>
                        </div>
                        <div>
                          <div className="font-medium">Vastu Score</div>
                          <div>{result.vastuScore}/10</div>
                        </div>
                        <div>
                          <div className="font-medium">Detailed Vastu</div>
                          <div>{result.hasDetailedVastu ? "✅ Yes" : "❌ No"}</div>
                        </div>
                      </div>
                    ) : (
                      <Alert variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertTitle>Test Failed</AlertTitle>
                        <AlertDescription>{result.error}</AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-gray-500">No test results yet. Run some tests to see results here.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
