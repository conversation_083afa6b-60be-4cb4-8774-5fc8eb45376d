import { createClient, SupabaseClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
  throw new Error('Missing environment variable NEXT_PUBLIC_SUPABASE_URL');
}
if (!supabaseAnonKey) {
  throw new Error('Missing environment variable NEXT_PUBLIC_SUPABASE_ANON_KEY');
}

// Export a single supabase client instance.
// For server-side operations where elevated privileges are needed (e.g., RLS bypass, admin tasks),
// a separate client instance should be created with the service role key.
// The default export remains the client-side safe anonymous key client.
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Function to get a Supabase client, using service role key if on server-side and key is available.
// This is a more explicit way to manage client instances based on context.
export function getSupabaseClient(useServiceRole: boolean = false): SupabaseClient {
  if (useServiceRole && typeof window === 'undefined' && supabaseServiceRoleKey) {
    // console.log("Using Supabase service role key for server-side operation.");
    return createClient(supabaseUrl!, supabaseServiceRoleKey, {
        // It's good practice to explicitly define auth options for service role clients
        auth: {
            persistSession: false, // Typically, service roles don't persist sessions
            autoRefreshToken: false,
            detectSessionInUrl: false,
        }
    });
  }
  // console.log("Using Supabase anonymous key.");
  return supabase; // Return the default client-side safe client
}

// Example of how you might get a service client specifically for server-side code:
// export const supabaseAdmin = typeof window === 'undefined' && supabaseServiceRoleKey 
//   ? createClient(supabaseUrl!, supabaseServiceRoleKey) 
//   : supabase; // Fallback to anon client or handle error if service key is mandatory and missing
// For this project, we will use getSupabaseClient(true) in server-side files where needed.