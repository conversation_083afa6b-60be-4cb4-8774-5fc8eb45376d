import { Badge } from "@/components/ui/badge"

interface ConfidenceIndicatorProps {
  score: number
  label?: string
}

export function ConfidenceIndicator({ score, label }: ConfidenceIndicatorProps) {
  const getColor = (score: number) => {
    if (score >= 0.8) return "bg-green-500"
    if (score >= 0.6) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getVariant = (score: number) => {
    if (score >= 0.8) return "default"
    if (score >= 0.6) return "secondary"
    return "destructive"
  }

  return (
    <div className="flex items-center gap-2">
      {label && <span className="text-xs text-gray-500">{label}</span>}
      <Badge variant={getVariant(score)} className="text-xs">
        {Math.round(score * 100)}% confidence
      </Badge>
    </div>
  )
}
