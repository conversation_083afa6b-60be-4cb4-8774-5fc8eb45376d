import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON>ir<PERSON>, AlertTriangle, Home, Calculator } from "lucide-react"
import { ConfidenceIndicator } from "@/components/confidence-indicator"
import { AnalysisChat } from "@/components/analysis-chat"

interface AnalysisResultsProps {
  analysisData: any
}

export function AnalysisResults({ analysisData }: AnalysisResultsProps) {
  // Safely extract data with fallbacks
  const projectInfo = analysisData?.projectInfo || {
    projectName: "Floor Plan Analysis",
    builderName: "Unknown Builder",
    city: "Unknown City",
    superArea: "1200",
    superAreaUnit: "sqft",
  }

  const rooms = analysisData?.rooms || []
  const overall_analysis = analysisData?.overall_analysis || {
    total_carpet_area_sqft: 0,
    total_carpet_area_sqm: 0,
    loading_percentage: null,
    vastu_compliance: { score: 0, observations: [], recommendations: [] },
    design_flaws: [],
    space_optimization: { score: 0, assessment: "", suggestions: [] },
    natural_light: { score: 0, assessment: "", recommendations: [] },
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Home className="w-5 h-5" />
            {projectInfo.projectName}
          </CardTitle>
          <p className="text-sm text-gray-600">
            {projectInfo.builderName} • {projectInfo.city}
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Super Area</p>
              <p className="font-semibold">
                {projectInfo.superArea} {projectInfo.superAreaUnit}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Carpet Area</p>
              <p className="font-semibold">{overall_analysis.total_carpet_area_sqft || 0} sq ft</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="summary" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="summary">Summary</TabsTrigger>
          <TabsTrigger value="rooms">Rooms</TabsTrigger>
          <TabsTrigger value="vastu">Vastu</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="chat">Ask AI</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="w-5 h-5" />
                Area Breakdown
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Total Carpet Area:</span>
                  <span className="font-semibold">{overall_analysis.total_carpet_area_sqft || 0} sq ft</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Carpet Area:</span>
                  <span className="font-semibold">{overall_analysis.total_carpet_area_sqm || 0} sq m</span>
                </div>
                <div className="flex justify-between">
                  <span>Loading:</span>
                  <span className="font-semibold">{overall_analysis.loading_percentage?.toFixed(1) || "N/A"}%</span>
                </div>
                {analysisData?.metadata && (
                  <div className="pt-2 border-t">
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>Analyzed by:</span>
                      <span>{analysisData.metadata.providerName || "AI"}</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Overall Scores</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Vastu Compliance</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${((overall_analysis.vastu_compliance?.score || 0) / 10) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium">{overall_analysis.vastu_compliance?.score || 0}/10</span>
                  </div>
                </div>

                {overall_analysis.space_optimization?.score && (
                  <div className="flex items-center justify-between">
                    <span>Space Optimization</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${(overall_analysis.space_optimization.score / 10) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium">{overall_analysis.space_optimization.score}/10</span>
                    </div>
                  </div>
                )}

                {overall_analysis.natural_light?.score && (
                  <div className="flex items-center justify-between">
                    <span>Natural Light</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-yellow-600 h-2 rounded-full"
                          style={{ width: `${(overall_analysis.natural_light.score / 10) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium">{overall_analysis.natural_light.score}/10</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rooms" className="space-y-4">
          {rooms.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No rooms identified in this floor plan.</p>
              </CardContent>
            </Card>
          ) : (
            rooms.map((room: any) => (
              <Card key={room.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{room.name}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{room.type?.replace("_", " ") || "Unknown"}</Badge>
                      {room.confidence_score && <ConfidenceIndicator score={room.confidence_score} />}
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 mb-3">
                    <div>
                      <p className="text-sm text-gray-600">Dimensions</p>
                      <p className="font-semibold">
                        {room.dimensions?.length || 0}' × {room.dimensions?.width || 0}'
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Area</p>
                      <p className="font-semibold">{room.dimensions?.area_sqft || 0} sq ft</p>
                      <p className="text-xs text-gray-500">{room.dimensions?.area_sqm || 0} sq m</p>
                    </div>
                  </div>
                  {room.features && room.features.length > 0 && (
                    <>
                      <p className="text-sm text-gray-600 mb-2">Features:</p>
                      <div className="flex gap-2 mb-3">
                        {room.features.map((feature: string, index: number) => (
                          <Badge key={index} variant="outline">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </>
                  )}
                  <p className="text-sm">{room.notes || "No additional notes"}</p>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>

        <TabsContent value="vastu" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Vastu Compliance Report</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                    <span className="text-xl font-bold text-green-600">
                      {overall_analysis.vastu_compliance?.score || 0}
                    </span>
                  </div>
                  <div>
                    <p className="font-semibold">Overall Vastu Score</p>
                    <p className="text-sm text-gray-600">Out of 10</p>
                  </div>
                </div>

                {overall_analysis.vastu_compliance?.observations?.map((observation: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm">{observation}</span>
                  </div>
                )) || <p className="text-gray-500 text-sm">No Vastu observations available.</p>}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Design Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-red-600 mb-2">Design Issues</h4>
                  {overall_analysis.design_flaws && overall_analysis.design_flaws.length > 0 ? (
                    overall_analysis.design_flaws.map((flaw: any, index: number) => (
                      <div key={index} className="border-l-4 border-red-500 pl-3 mb-3">
                        <div className="flex items-center gap-2 mb-1">
                          <AlertTriangle className="w-4 h-4 text-red-600" />
                          <span className="font-medium">{flaw.issue || flaw}</span>
                          {flaw.severity && (
                            <Badge
                              variant={
                                flaw.severity === "high"
                                  ? "destructive"
                                  : flaw.severity === "medium"
                                    ? "default"
                                    : "secondary"
                              }
                            >
                              {flaw.severity}
                            </Badge>
                          )}
                        </div>
                        {flaw.location && <p className="text-sm text-gray-600">Location: {flaw.location}</p>}
                        {flaw.suggestion && <p className="text-sm text-blue-600">Suggestion: {flaw.suggestion}</p>}
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-sm">No design issues identified.</p>
                  )}
                </div>

                {overall_analysis.space_optimization && (
                  <div>
                    <h4 className="font-semibold text-blue-600 mb-2">Space Optimization</h4>
                    <p className="text-sm mb-2">
                      {overall_analysis.space_optimization.assessment || "No assessment available"}
                    </p>
                    {overall_analysis.space_optimization.suggestions?.map((suggestion: string, index: number) => (
                      <div key={index} className="flex items-center gap-2 mb-1">
                        <CheckCircle className="w-4 h-4 text-blue-600" />
                        <span className="text-sm">{suggestion}</span>
                      </div>
                    ))}
                  </div>
                )}

                {overall_analysis.natural_light && (
                  <div>
                    <h4 className="font-semibold text-yellow-600 mb-2">Natural Light & Ventilation</h4>
                    <p className="text-sm mb-2">
                      {overall_analysis.natural_light.assessment || "No assessment available"}
                    </p>
                    {overall_analysis.natural_light.recommendations?.map((rec: string, index: number) => (
                      <div key={index} className="flex items-center gap-2 mb-1">
                        <CheckCircle className="w-4 h-4 text-yellow-600" />
                        <span className="text-sm">{rec}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chat">
          <AnalysisChat analysisData={analysisData} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
