# Admin Dashboard for Model and API Key Management: Detailed Roadmap

This document provides a comprehensive development roadmap for creating an admin dashboard to manage Large Language Model (LLM) providers, specific models, and their associated API keys for the Floor Plan Analyzer application. The goal is to centralize configuration, enhance flexibility, and improve security and manageability of LLM integrations.

## Core Need for a Database and Admin Dashboard

Initially, LLM provider details and API keys were hardcoded or managed solely through environment variables. This approach presented several limitations:

1.  **Scalability**: Adding new models or providers required code changes and redeployments.
2.  **Flexibility**: Switching between models or updating API keys was cumbersome.
3.  **Security**: Managing multiple API keys directly in environment variables can become unwieldy and less secure if not handled with extreme care, especially across different environments (dev, staging, prod).
4.  **Management**: No central place to view active models, providers, or key statuses.
5.  **Dynamic Configuration**: Inability to enable/disable models or providers without code changes.

A database-driven approach, managed via an admin dashboard, addresses these issues by:

*   **Centralizing Configuration**: Storing all model, provider, and API key information in a structured database.
*   **Dynamic Management**: Allowing administrators to add, edit, activate/deactivate models, providers, and keys through a user interface without code changes.
*   **Improved Security Potential**: Facilitating better API key management practices, including future implementation of encryption at rest and rotation policies.
*   **Auditability & Tracking (Future)**: Paving the way for tracking API key usage and model performance.

## Phase 1: Backend Foundation (Completed)

This phase laid the groundwork for dynamically managing LLM configurations by setting up a database and refactoring the core application logic to use it.

### 1.1. Database Setup (Supabase)

*   **Why Supabase?**: Supabase was chosen for its ease of integration with Next.js, built-in authentication, real-time capabilities, and generous free tier, making it suitable for rapid development and deployment.
*   **Creation Process**:
    *   [x] **User Action**: A new project was created on the Supabase platform ([supabase.com](https://supabase.com)). This involved:
        1.  Signing up/logging into Supabase.
        2.  Clicking on "New Project".
        3.  Naming the project (e.g., `floorplan-analyzer-admin`).
        4.  Setting a strong database password (stored securely by the user).
        5.  Choosing a region.
        6.  Selecting the free plan (or a paid plan as needed).
*   **Access Credentials Provided by User**:
    *   [x] **Project URL**: The unique URL for the Supabase project's API (e.g., `https://<your-project-ref>.supabase.co`).
    *   [x] **Anon (Public) Key**: A client-side public key used for accessing Supabase services from the browser, respecting Row Level Security (RLS) policies.
*   **Environment Variable Configuration (`.env.local`)**:
    *   [x] **Purpose**: To securely store and manage Supabase credentials for local development.
    *   [x] **File Created/Updated**: `.env.local` in the project root.
    *   [x] **Variables Added**:
        *   `NEXT_PUBLIC_SUPABASE_URL="<USER_PROVIDED_PROJECT_URL>"`: The public URL for Supabase.
        *   `NEXT_PUBLIC_SUPABASE_ANON_KEY="<USER_PROVIDED_ANON_KEY>"`: The public anon key.
        *   `SUPABASE_SERVICE_ROLE_KEY="<USER_RETRIEVED_SERVICE_KEY>"`: A secret key with full access to the database, bypassing RLS. This key was retrieved by the user from their Supabase project settings (Project Settings -> API -> Project API keys -> `service_role` secret) and **must be kept confidential and never exposed on the client-side.**
    *   [x] **`.gitignore`**: Ensured `.env.local` is listed in `.gitignore` to prevent accidental commits of secrets.

### 1.2. Database Schema Definition

*   **Purpose**: To define the structure for storing information about model providers, API keys, and models.
*   **Creation Method**: SQL statements were executed directly in the Supabase SQL Editor (Supabase Dashboard -> SQL Editor -> "New query").
*   **Tables Created**:
    *   [x] **`ModelProviders` Table**:
        *   **Purpose**: Stores information about different LLM providers (e.g., OpenAI, Anthropic, Google).
        *   **SQL Schema**:
            ```sql
            CREATE TABLE ModelProviders (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                name TEXT NOT NULL UNIQUE, -- e.g., "OpenAI", "Anthropic"
                slug TEXT NOT NULL UNIQUE, -- e.g., "openai", "anthropic" (for programmatic access)
                api_base_url TEXT, -- Optional: if the provider has a non-standard base URL
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            ```
    *   [x] **`ProviderAPIKeys` Table**:
        *   **Purpose**: Stores API keys associated with each model provider. Allows for multiple keys per provider for rotation and management.
        *   **SQL Schema**:
            ```sql
            CREATE TABLE ProviderAPIKeys (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                provider_id UUID NOT NULL REFERENCES ModelProviders(id) ON DELETE CASCADE,
                api_key_value TEXT NOT NULL, -- Actual API key. Encryption at rest is a Phase 3 goal.
                is_active BOOLEAN DEFAULT TRUE,
                use_count INTEGER DEFAULT 0,
                last_used_at TIMESTAMPTZ,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            ```
        *   *Security Note*: API keys are currently stored as plain text. Encryption at rest is a critical enhancement planned for Phase 3.
    *   [x] **`Models` Table**:
        *   **Purpose**: Stores details about specific LLM models offered by the providers.
        *   **SQL Schema**:
            ```sql
            CREATE TABLE Models (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                provider_id UUID NOT NULL REFERENCES ModelProviders(id) ON DELETE CASCADE,
                name TEXT NOT NULL, -- e.g., "GPT-4o", "Claude 3 Opus"
                model_identifier TEXT NOT NULL, -- e.g., "gpt-4o", "claude-3-opus-20240229"
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW(),
                UNIQUE (provider_id, model_identifier) -- Ensure unique model per provider
            );
            ```

### 1.3. Supabase Client Library & Configuration

*   **Purpose**: To enable interaction with the Supabase database from the Next.js application.
*   **Actions Taken**:
    *   [x] **Installed `@supabase/supabase-js` library**: `pnpm add @supabase/supabase-js` was run.
    *   [x] **Created `lib/supabaseClient.ts`**: This utility file initializes and exports Supabase client instances.
        *   **Content Overview**:
            ```typescript
            // lib/supabaseClient.ts
            import { createClient } from '@supabase/supabase-js';

            const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
            const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
            const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

            // Public client (uses anon key, for client-side operations)
            export const supabase = createClient(supabaseUrl, supabaseAnonKey);

            // Function to get a Supabase client, potentially with service role key for server-side operations
            export const getSupabaseClient = (useServiceRole = false) => {
              if (useServiceRole) {
                if (!supabaseServiceRoleKey) {
                  throw new Error('SUPABASE_SERVICE_ROLE_KEY is not set. This key is required for server-side admin operations.');
                }
                // Create a new client instance for service role to ensure it's not shared/cached incorrectly
                return createClient(supabaseUrl, supabaseServiceRoleKey, {
                  auth: { persistSession: false } // Important for server-side usage
                });
              }
              return supabase; // Return the default public client
            };
            ```
        *   **Key Features**: Provides a default client using the `anon` key for public, client-side interactions, and a function `getSupabaseClient(true)` to obtain a client using the `service_role` key for secure server-side operations that require elevated privileges.

### 1.4. Refactor Core Logic to Use Database

*   **Purpose**: To shift from hardcoded LLM configurations to dynamic fetching from the Supabase database.
*   **Files Modified**:
    *   [x] **`lib/analyze-floor-plan.ts`**:
        *   [x] **Removed Hardcoded `llmProviders`**: The static object defining providers and models was deleted.
        *   [x] **Implemented `getModelInstance(modelIdentifier: string, providerSlug: string)`**: 
            *   Fetches the specified model's details from the `Models` table.
            *   Fetches an active API key for the model's provider from the `ProviderAPIKeys` table.
            *   Constructs and returns the LLM instance (e.g., `ChatOpenAI`, `ChatAnthropic`).
            *   Uses `getSupabaseClient(true)` for database queries, ensuring server-side privileges.
        *   [x] **Updated `getAvailableProviders()`**: 
            *   Queries Supabase for all `Models` that are `is_active = TRUE` and have an associated `ModelProvider` that is `is_active = TRUE` and at least one `ProviderAPIKeys` that is `is_active = TRUE`.
            *   Returns a list of available models grouped by provider, suitable for populating UI selectors.
            *   Uses `getSupabaseClient(true)` for database queries.
    *   [x] **`app/api/check-env/route.ts`**:
        *   [x] **Modified to use `getAvailableProviders()`**: Instead of checking individual environment variables for API keys, this endpoint now calls `getAvailableProviders()` from `lib/analyze-floor-plan.ts` to determine model availability based on database configurations.
        *   This aligns the environment/status check with the new database-driven approach.

## Phase 2: Frontend UI - Admin Dashboard (Next Steps)

This phase focuses on building the user interface for administrators to manage the LLM configurations stored in Supabase.

### 2.1. Admin Section Scaffolding

*   **Goal**: Create the basic structure and access control for the admin dashboard.
*   **Tasks**:
    *   [ ] **Create Admin Route**:
        *   **Instruction**: Create a new route group or directory structure for admin pages, e.g., `app/admin/...`.
        *   **Example**: `app/admin/dashboard/page.tsx` could be the main admin landing page.
    *   [ ] **Implement Basic Layout**:
        *   **Instruction**: Design a layout specific to the admin section. This typically includes a sidebar for navigation (e.g., Manage Providers, Manage Models) and a main content area to display forms and tables.
        *   **Considerations**: Reuse existing UI components from `components/ui` where possible (e.g., Card, Table, Button).
    *   [ ] **Authentication & Authorization (Supabase Auth)**:
        *   **Goal**: Secure the `/admin` routes so only authenticated users with a designated 'admin' role can access them.
        *   **Instructions**:
            1.  **Setup Supabase Auth**: If not already set up for general user authentication, configure Supabase Auth (Email/Password, OAuth providers as needed).
            2.  **Admin Role Management**: Decide how to assign an 'admin' role. This could be:
                *   Manually assigning a role/custom claim to a user in the Supabase dashboard.
                *   Creating a separate table for user roles linked to the `auth.users` table.
            3.  **Protect Admin Routes**: 
                *   Use Next.js Middleware (`middleware.ts`) or server-side checks in page components/layouts to verify authentication and the 'admin' role.
                *   Redirect non-admin users or unauthenticated users away from `/admin` routes.
                *   **Example (Middleware Concept)**:
                    ```typescript
                    // middleware.ts
                    import { createServerClient } from '@supabase/ssr';
                    import { NextResponse, type NextRequest } from 'next/server';

                    export async function middleware(request: NextRequest) {
                      const supabase = createServerClient(...); // Initialize Supabase client for middleware
                      const { data: { user } } = await supabase.auth.getUser();

                      if (request.nextUrl.pathname.startsWith('/admin')) {
                        if (!user) {
                          return NextResponse.redirect(new URL('/login', request.url));
                        }
                        // Add role check here, e.g., by fetching user metadata or a roles table
                        // const { data: userRole } = await supabase.from('user_roles').select('role').eq('user_id', user.id).single();
                        // if (userRole?.role !== 'admin') {
                        //   return NextResponse.redirect(new URL('/', request.url)); // Or an unauthorized page
                        // }
                      }
                      return NextResponse.next();
                    }
                    ```

### 2.2. UI for Managing Model Providers (`/admin/providers`)

*   **Goal**: Allow admins to CRUD (Create, Read, Update, Delete) model providers.
*   **Tasks**:
    *   [ ] **List View (Table)**:
        *   **Instruction**: Create a page (e.g., `app/admin/providers/page.tsx`) to display all `ModelProviders` from Supabase.
        *   **Columns**: Name, Slug, API Base URL (if any), Active status, Actions (Edit, Delete).
        *   **Component**: Use `components/ui/table` or a similar component.
    *   [ ] **Create Form (`/admin/providers/new`)**:
        *   **Instruction**: A page or modal with a form to add a new model provider.
        *   **Fields**: Name (text input), Slug (text input, potentially auto-generated from name), API Base URL (optional text input), Active (switch/checkbox).
        *   **Component**: Use `components/ui/form`, `input`, `button`, `switch`.
    *   [ ] **Edit Form (`/admin/providers/[id]/edit`)**:
        *   **Instruction**: Similar to the create form, but pre-filled with existing provider data.
    *   [ ] **Delete Functionality**:
        *   **Instruction**: A button/icon in the list view or edit form to delete a provider.
        *   **Considerations**: Implement a confirmation dialog (`AlertDialog`). Handle cascading deletes or prevent deletion if the provider is linked to active `Models` (show an error or provide options).

### 2.3. UI for Managing API Keys per Provider (`/admin/providers/[providerId]/keys`)

*   **Goal**: Allow admins to manage API keys for a specific provider.
*   **Tasks**:
    *   [ ] **List View (Table, on Provider Detail Page or separate page)**:
        *   **Instruction**: Display `ProviderAPIKeys` for a selected provider.
        *   **Columns**: Key Value (masked, e.g., `sk-xxxx...xxxx`), Active status, Use Count, Last Used, Actions (Edit, Delete).
    *   [ ] **Add API Key Form**:
        *   **Instruction**: Form to add a new API key.
        *   **Fields**: API Key Value (password input or textarea), Active (switch/checkbox).
        *   *Security Reminder*: Key is stored as-is for now. Encryption is Phase 3.
    *   [ ] **Edit API Key Form**:
        *   **Instruction**: Primarily to toggle 'Active' status. Editing the key value itself should likely mean deleting and re-adding for clarity and security.
    *   [ ] **Delete API Key Functionality**:
        *   **Instruction**: Button/icon to delete a key, with confirmation.

### 2.4. UI for Managing Models (`/admin/models`)

*   **Goal**: Allow admins to CRUD models.
*   **Tasks**:
    *   [ ] **List View (Table)**:
        *   **Instruction**: Page (e.g., `app/admin/models/page.tsx`) to display all `Models`.
        *   **Columns**: Name, Model Identifier, Provider (name, fetched via relation), Description, Active status, Actions (Edit, Delete).
    *   [ ] **Create Form (`/admin/models/new`)**:
        *   **Instruction**: Form to add a new model.
        *   **Fields**: Name (text input), Model Identifier (text input, e.g., `gpt-4o`), Provider (select dropdown populated from `ModelProviders`), Description (textarea), Active (switch/checkbox).
    *   [ ] **Edit Form (`/admin/models/[id]/edit`)**:
        *   **Instruction**: Similar to create, pre-filled with existing model data.
    *   [ ] **Delete Functionality**:
        *   **Instruction**: Button/icon to delete a model, with confirmation.

### 2.5. Backend APIs for Admin CRUD Operations

*   **Goal**: Create server-side API endpoints to handle data manipulation requests from the admin frontend.
*   **Tasks**:
    *   [ ] **Create Next.js API Routes**:
        *   **Instruction**: Set up API routes under `app/api/admin/...` (e.g., `app/api/admin/providers/route.ts`, `app/api/admin/models/[id]/route.ts`).
        *   **HTTP Methods**: Implement handlers for `GET` (list, retrieve one), `POST` (create), `PUT`/`PATCH` (update), `DELETE`.
    *   [ ] **Supabase Interaction**:
        *   **Instruction**: Use `getSupabaseClient(true)` (service role key) for all database operations within these API routes to bypass RLS if necessary for admin actions.
    *   [ ] **Validation & Error Handling**:
        *   **Instruction**: Implement input validation (e.g., using Zod) for all incoming data.
        *   Provide clear error responses with appropriate HTTP status codes.
    *   [ ] **Security**: Ensure these API routes are also protected by the same authentication and authorization logic as the frontend admin pages (e.g., check for admin role).

## Phase 3: Advanced Features & Refinements

This phase focuses on enhancing security, usability, and adding more sophisticated features.

### 3.1. API Key Encryption at Rest

*   **Goal**: Securely encrypt API keys stored in the `ProviderAPIKeys.api_key_value` column.
*   **Tasks**:
    *   [ ] **Research Encryption Strategy**:
        *   **Instruction**: Investigate options like:
            *   **Supabase Vault (if available and suitable)**: Check Supabase's offerings for secrets management.
            *   **Application-Level Encryption**: Using libraries like `crypto` (Node.js built-in) or `libsodium`.
            *   **Key Management**: How to manage the master encryption key securely (e.g., environment variable, dedicated secrets manager like HashiCorp Vault, AWS KMS, Google Cloud KMS).
    *   [ ] **Implement Encryption/Decryption Logic**:
        *   **Instruction**: Modify backend API routes (for creating/updating keys) to encrypt keys before saving to DB.
        *   Modify `lib/analyze-floor-plan.ts` (`getModelInstance`) to decrypt keys when fetching them for use.
    *   [ ] **Data Migration (if existing keys)**:
        *   **Instruction**: Plan and execute a script to encrypt any existing plaintext API keys in the database.

### 3.2. API Key Rotation Feature

*   **Goal**: Make it easy for admins to update API keys without service interruption.
*   **Tasks**:
    *   [ ] **UI Enhancements**:
        *   **Instruction**: In the API Keys management UI, add a feature to "Add New & Deactivate Old" or a clear workflow for adding a new key and then deactivating/deleting the old one.
    *   [ ] **Backend Logic**: Ensure the system can gracefully switch to a new key once it's marked active.

### 3.3. Usage Tracking & Basic Analytics (Optional but Recommended)

*   **Goal**: Provide insights into API key and model usage.
*   **Tasks**:
    *   [ ] **Enhance `getModelInstance`**:
        *   **Instruction**: Update `getModelInstance` in `lib/analyze-floor-plan.ts` to increment `use_count` and update `last_used_at` for the `ProviderAPIKeys` entry that was used.
    *   [ ] **Display Stats in Admin UI**:
        *   **Instruction**: Add columns or sections in the admin dashboard to show `use_count` and `last_used_at` for API keys and potentially aggregate usage for models/providers.

### 3.4. Enhanced User Feedback and Error Handling

*   **Goal**: Improve the user experience in the admin dashboard.
*   **Tasks**:
    *   [ ] **Implement Toasts/Alerts**:
        *   **Instruction**: Use a notification system (e.g., `sonner` or `react-hot-toast` if already in use or add one) to provide feedback for successful actions (e.g., "Provider created successfully") and errors.
    *   [ ] **Robust Error Display**:
        *   **Instruction**: Ensure that errors from API calls are caught and displayed clearly to the admin user, guiding them on how to resolve if possible.

### 3.5. Testing

*   **Goal**: Ensure the reliability and correctness of the admin dashboard and backend logic.
*   **Tasks**:
    *   [ ] **Manual Testing**: Thoroughly test all CRUD operations for providers, keys, and models.
    *   [ ] **Integration Testing**: Test the end-to-end flow: add a model via admin, then verify it can be used by the `analyzeFloorPlan` function.
    *   [ ] **Security Testing**: Verify authentication and authorization for admin routes and APIs.
    *   [ ] **(Optional) Automated Testing**: Consider adding unit tests for critical backend logic (e.g., API handlers, database interaction functions) and end-to-end tests for UI flows (e.g., using Playwright or Cypress).

### 3.6. Documentation

*   **Goal**: Document the new system for maintainability and user guidance.
*   **Tasks**:
    *   [ ] **Admin Dashboard Usage Guide**:
        *   **Instruction**: Create a simple guide on how to use the admin dashboard: how to log in, manage providers, keys, and models.
    *   [ ] **Technical Documentation**:
        *   **Instruction**: Update or create documentation for the new database schema, API endpoints, and any significant changes to the application architecture.
        *   Ensure environment variable setup (including `SUPABASE_SERVICE_ROLE_KEY`) is clearly documented for new developers.

---

We will proceed with **Phase 2: Frontend UI - Admin Dashboard**, starting with **2.1. Admin Section Scaffolding** and **Authentication & Authorization**.