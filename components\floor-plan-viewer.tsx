"use client"

import { useEffect, useRef, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Share2, Edit, ZoomIn, ZoomOut, RotateCw } from "lucide-react"
import { PDFExportButton } from "@/components/pdf-export-button"

interface FloorPlanViewerProps {
  analysisData: any
}

export function FloorPlanViewer({ analysisData }: FloorPlanViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement | null>(null)
  const [zoom, setZoom] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [isImageLoaded, setIsImageLoaded] = useState(false)
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Set image URL based on analysis data
  useEffect(() => {
    try {
      let url = null

      if (analysisData?.sampleImageUrl) {
        url = analysisData.sampleImageUrl
      } else if (analysisData?.metadata?.fileInfo?.name) {
        // In a real app, this would be the URL to the uploaded image
        url =
          "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/1490602110110.jpg-G1rArPGf6ttaDdQl1VEwfCrts4aINg.jpeg"
      } else {
        // Default sample image
        url =
          "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/1490602110110.jpg-G1rArPGf6ttaDdQl1VEwfCrts4aINg.jpeg"
      }

      setImageUrl(url)
      setImageError(false)
      setIsImageLoaded(false)
    } catch (error) {
      console.error("Error setting image URL:", error)
      setImageError(true)
    }
  }, [analysisData])

  // Draw canvas when image loads or parameters change
  useEffect(() => {
    if (!imageUrl || !isImageLoaded || imageError) return

    const drawCanvas = () => {
      const canvas = canvasRef.current
      if (!canvas) return

      const ctx = canvas.getContext("2d")
      if (!ctx) return

      const img = imageRef.current
      if (!img) return

      try {
        // Set canvas size to match container
        const container = canvas.parentElement
        if (container) {
          const rect = container.getBoundingClientRect()
          canvas.width = rect.width
          canvas.height = rect.height
        }

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        // Save context state
        ctx.save()

        // Translate to center
        ctx.translate(canvas.width / 2, canvas.height / 2)

        // Apply transformations
        ctx.scale(zoom, zoom)
        ctx.rotate((rotation * Math.PI) / 180)

        // Calculate image size to fit canvas
        const maxWidth = canvas.width * 0.8
        const maxHeight = canvas.height * 0.8
        const scale = Math.min(maxWidth / img.naturalWidth, maxHeight / img.naturalHeight)

        const imgWidth = img.naturalWidth * scale
        const imgHeight = img.naturalHeight * scale

        // Draw image
        ctx.drawImage(img, -imgWidth / 2, -imgHeight / 2, imgWidth, imgHeight)

        // Draw room overlays if available
        if (analysisData?.rooms && Array.isArray(analysisData.rooms)) {
          analysisData.rooms.forEach((room: any, index: number) => {
            if (!room) return

            try {
              const hue = (index * 45) % 360
              ctx.fillStyle = `hsla(${hue}, 70%, 60%, 0.2)`
              ctx.strokeStyle = `hsla(${hue}, 70%, 40%, 0.8)`
              ctx.lineWidth = 2

              // Simple grid layout for room overlays
              const cols = 3
              const roomWidth = (imgWidth / cols) * 0.8
              const roomHeight = (imgHeight / Math.ceil(analysisData.rooms.length / cols)) * 0.8

              const col = index % cols
              const row = Math.floor(index / cols)

              const x = -imgWidth / 2 + col * (imgWidth / cols) + (imgWidth / cols - roomWidth) / 2
              const y =
                -imgHeight / 2 +
                row * (imgHeight / Math.ceil(analysisData.rooms.length / cols)) +
                (imgHeight / Math.ceil(analysisData.rooms.length / cols) - roomHeight) / 2

              // Draw room rectangle
              ctx.fillRect(x, y, roomWidth, roomHeight)
              ctx.strokeRect(x, y, roomWidth, roomHeight)

              // Draw room label
              ctx.fillStyle = "black"
              ctx.font = `bold ${Math.max(10, 14 * scale)}px Arial`
              ctx.textAlign = "center"
              ctx.textBaseline = "middle"

              const roomName = room.name || `Room ${index + 1}`
              ctx.fillText(roomName, x + roomWidth / 2, y + roomHeight / 2)
            } catch (roomError) {
              console.error("Error drawing room:", roomError)
            }
          })
        }

        // Restore context
        ctx.restore()
      } catch (error) {
        console.error("Error drawing canvas:", error)
      }
    }

    drawCanvas()
  }, [imageUrl, isImageLoaded, zoom, rotation, analysisData?.rooms, imageError])

  // Handle image load
  const handleImageLoad = () => {
    setIsImageLoaded(true)
    setImageError(false)
    setIsLoading(false)
  }

  // Handle image error
  const handleImageError = () => {
    console.error("Failed to load image:", imageUrl)
    setImageError(true)
    setIsImageLoaded(false)
    setIsLoading(false)
  }

  // Control handlers
  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.2, 3))
  }

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.2, 0.5))
  }

  const handleRotate = () => {
    setRotation((prev) => (prev + 90) % 360)
  }

  // Load image when URL changes
  useEffect(() => {
    if (!imageUrl) return

    setIsLoading(true)
    setIsImageLoaded(false)
    setImageError(false)

    const img = new Image()
    img.crossOrigin = "anonymous"

    img.onload = handleImageLoad
    img.onerror = handleImageError

    img.src = imageUrl
    imageRef.current = img

    return () => {
      if (imageRef.current) {
        imageRef.current.onload = null
        imageRef.current.onerror = null
      }
    }
  }, [imageUrl])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Floor Plan Analysis</span>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleZoomIn} disabled={!isImageLoaded}>
                <ZoomIn className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleZoomOut} disabled={!isImageLoaded}>
                <ZoomOut className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleRotate} disabled={!isImageLoaded}>
                <RotateCw className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
              <PDFExportButton analysisData={analysisData} visualizationElementId="floor-plan-canvas-container" />
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            id="floor-plan-canvas-container"
            className="aspect-square bg-gray-100 rounded-lg overflow-hidden relative"
          >
            {imageUrl && !imageError ? (
              <>
                <canvas
                  ref={canvasRef}
                  className="w-full h-full"
                  style={{ display: isImageLoaded ? "block" : "none" }}
                />
                {(isLoading || !isImageLoaded) && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                      <p className="text-sm text-gray-600">Loading floor plan...</p>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center flex items-center justify-center h-full">
                <div>
                  <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                      />
                    </svg>
                  </div>
                  <p className="text-gray-600">
                    {imageError ? "Failed to load floor plan" : "No floor plan available"}
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    {imageError
                      ? "Please try refreshing the page"
                      : "Upload a floor plan to see the interactive analysis overlay"}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Room Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            {analysisData?.rooms && Array.isArray(analysisData.rooms) && analysisData.rooms.length > 0 ? (
              analysisData.rooms.map((room: any, index: number) => {
                if (!room) return null

                return (
                  <div key={room.id || `room-${index}`} className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: `hsl(${index * 45}, 70%, 60%)` }} />
                    <span className="text-sm">{room.name || `Room ${index + 1}`}</span>
                  </div>
                )
              })
            ) : (
              <p className="text-sm text-gray-500 col-span-2">No rooms identified</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
