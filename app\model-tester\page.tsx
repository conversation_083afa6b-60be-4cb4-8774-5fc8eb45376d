"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, AlertTriangle, Clock, Brain, Zap, RefreshCw, ExternalLink } from "lucide-react"

interface ModelTestResult {
  id: string
  name: string
  success: boolean
  message?: string
  error?: string
  responseTime: number
  setupUrl?: string
  envKey?: string
}

export default function ModelTesterPage() {
  const [availableProviders, setAvailableProviders] = useState<
    Array<{
      id: string
      name: string
      description: string
    }>
  >([])
  const [testResults, setTestResults] = useState<ModelTestResult[]>([])
  const [isRunningTests, setIsRunningTests] = useState(false)
  const [currentTest, setCurrentTest] = useState<string>("")
  const [overallStatus, setOverallStatus] = useState<{
    total: number
    success: number
    failed: number
    pending: number
  }>({
    total: 0,
    success: 0,
    failed: 0,
    pending: 0,
  })

  useEffect(() => {
    fetchAvailableProviders()
  }, [])

  useEffect(() => {
    // Update overall status whenever test results change
    const total = availableProviders.length
    const success = testResults.filter((r) => r.success).length
    const failed = testResults.filter((r) => !r.success).length
    const pending = total - success - failed

    setOverallStatus({
      total,
      success,
      failed,
      pending,
    })
  }, [testResults, availableProviders])

  const fetchAvailableProviders = async () => {
    try {
      // Define all possible providers with updated names
      const allProviders = [
        { id: "groq", name: "Groq Llama 4 Scout", description: "Ultra-fast processing" },
        { id: "openai", name: "OpenAI GPT-4o", description: "Highly accurate analysis" },
        { id: "openai-gpt4-1", name: "OpenAI GPT-4.1", description: "Latest OpenAI model" },
        { id: "openai-gpt4-1-mini", name: "OpenAI GPT-4.1 Mini", description: "Faster GPT-4.1" },
        { id: "anthropic", name: "Claude 3.5 Sonnet", description: "Excellent architectural analysis" },
        { id: "gemini", name: "Gemini 2.0 Flash", description: "Fast and efficient" },
        { id: "gemini-2-5", name: "Gemini 2.5 Flash", description: "Enhanced vision capabilities" },
        { id: "gemini-2-5-pro", name: "Gemini 2.5 Pro", description: "Most advanced Google model" },
        { id: "mistral", name: "Mistral Large", description: "High-quality multilingual analysis" },
        { id: "mistral-small", name: "Mistral Small", description: "Fast and efficient Mistral model" },
        { id: "mistral-medium", name: "Mistral Medium", description: "Balanced performance Mistral model" },
        { id: "deepseek", name: "DeepSeek Chat", description: "Advanced reasoning capabilities" },
        { id: "qwen", name: "Qwen 2.5 Max", description: "Strong spatial understanding" },
        { id: "openrouter-gpt4o", name: "OpenRouter GPT-4o", description: "GPT-4o via OpenRouter" },
        { id: "openrouter-claude", name: "OpenRouter Claude", description: "Claude via OpenRouter" },
        { id: "openrouter-gemini", name: "OpenRouter Gemma", description: "Gemma via OpenRouter" },
        { id: "openrouter-qwen", name: "OpenRouter Qwen VL", description: "Qwen VL via OpenRouter" },
        { id: "openrouter-llama", name: "OpenRouter Llama Vision", description: "Llama Vision via OpenRouter" },
      ]

      setAvailableProviders(allProviders)
    } catch (error) {
      console.error("Failed to fetch providers:", error)
    }
  }

  const testSingleModel = async (provider: string): Promise<ModelTestResult> => {
    setCurrentTest(provider)
    const providerInfo = availableProviders.find((p) => p.id === provider) || {
      id: provider,
      name: provider,
      description: "",
    }

    const startTime = Date.now()

    try {
      const response = await fetch("/api/test-model", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          provider,
        }),
      })

      const data = await response.json()
      const endTime = Date.now()

      if (data.success) {
        return {
          id: provider,
          name: providerInfo.name,
          success: true,
          message: data.message,
          responseTime: endTime - startTime,
        }
      } else {
        return {
          id: provider,
          name: providerInfo.name,
          success: false,
          error: data.error || "Unknown error",
          responseTime: endTime - startTime,
          setupUrl: data.setupUrl,
          envKey: data.envKey,
        }
      }
    } catch (error) {
      return {
        id: provider,
        name: providerInfo.name,
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        responseTime: Date.now() - startTime,
      }
    }
  }

  const testAllModels = async () => {
    setIsRunningTests(true)
    setTestResults([])

    for (const provider of availableProviders) {
      const result = await testSingleModel(provider.id)
      setTestResults((prev) => [...prev, result])
    }

    setIsRunningTests(false)
    setCurrentTest("")
  }

  const getStatusBadge = (success: boolean) => {
    if (success) {
      return (
        <Badge className="bg-green-500">
          <CheckCircle className="w-3 h-3 mr-1" />
          Success
        </Badge>
      )
    } else {
      return (
        <Badge variant="destructive">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Failed
        </Badge>
      )
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">AI Model Tester</h1>
        <p className="text-gray-600">Test all AI models to verify their connectivity and functionality</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Brain className="w-5 h-5" />
              Total Models
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStatus.total}</div>
            <p className="text-sm text-gray-600">Available for testing</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <CheckCircle className="w-5 h-5 text-green-500" />
              Success
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">{overallStatus.success}</div>
            <p className="text-sm text-gray-600">Models working</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              Failed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">{overallStatus.failed}</div>
            <p className="text-sm text-gray-600">Models with errors</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Clock className="w-5 h-5 text-gray-500" />
              Pending
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-500">{overallStatus.pending}</div>
            <p className="text-sm text-gray-600">Models not tested</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Model Test Results</span>
            <Button onClick={testAllModels} disabled={isRunningTests} className="flex items-center gap-2" size="sm">
              {isRunningTests ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4" />
                  Test All Models
                </>
              )}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isRunningTests && currentTest && (
            <Alert className="mb-4">
              <Clock className="h-4 w-4" />
              <AlertTitle>Testing in progress</AlertTitle>
              <AlertDescription>Currently testing: {currentTest}</AlertDescription>
            </Alert>
          )}

          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="py-2 px-4 text-left">Model</th>
                  <th className="py-2 px-4 text-left">Status</th>
                  <th className="py-2 px-4 text-left">Response Time</th>
                  <th className="py-2 px-4 text-left">Details</th>
                </tr>
              </thead>
              <tbody>
                {testResults.length > 0 ? (
                  testResults.map((result) => (
                    <tr key={result.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium">{result.name}</div>
                        <div className="text-xs text-gray-500">{result.id}</div>
                      </td>
                      <td className="py-3 px-4">{getStatusBadge(result.success)}</td>
                      <td className="py-3 px-4">{result.responseTime}ms</td>
                      <td className="py-3 px-4">
                        {result.success ? (
                          <span className="text-green-600 text-sm">{result.message}</span>
                        ) : (
                          <div>
                            <span className="text-red-600 text-sm">{result.error}</span>
                            {result.setupUrl && (
                              <div className="mt-1">
                                <a
                                  href={result.setupUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:underline text-xs flex items-center gap-1"
                                >
                                  Setup API Key <ExternalLink className="w-3 h-3" />
                                </a>
                              </div>
                            )}
                          </div>
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="py-8 text-center text-gray-500">
                      {isRunningTests ? "Running tests..." : "No tests run yet. Click 'Test All Models' to begin."}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
