"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Loader2, CheckCircle, AlertTriangle } from "lucide-react"
import type { LLMProvider } from "@/lib/analyze-floor-plan"

interface ModelTesterProps {
  availableProviders: Array<{
    id: LLMProvider
    name: string
    description: string
  }>
}

export function ModelTester({ availableProviders }: ModelTesterProps) {
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider | "">("")
  const [testStatus, setTestStatus] = useState<"idle" | "testing" | "success" | "error">("idle")
  const [testResult, setTestResult] = useState<string>("")

  const handleTest = async () => {
    if (!selectedProvider) return

    setTestStatus("testing")
    setTestResult("")

    try {
      const response = await fetch("/api/test-model", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          provider: selectedProvider,
        }),
      })

      const data = await response.json()

      if (data.success) {
        setTestStatus("success")
        setTestResult(data.message || "Model test successful!")
      } else {
        setTestStatus("error")
        setTestResult(data.error || "Model test failed.")
      }
    } catch (error) {
      setTestStatus("error")
      setTestResult(error instanceof Error ? error.message : "An unknown error occurred")
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>AI Model Tester</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Select AI Model to Test</label>
          <Select value={selectedProvider} onValueChange={(value) => setSelectedProvider(value as LLMProvider)}>
            <SelectTrigger>
              <SelectValue placeholder="Select a model" />
            </SelectTrigger>
            <SelectContent>
              {availableProviders.map((provider) => (
                <SelectItem key={provider.id} value={provider.id}>
                  <div>
                    <div className="font-medium">{provider.name}</div>
                    <div className="text-xs text-gray-500">{provider.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button onClick={handleTest} disabled={!selectedProvider || testStatus === "testing"} className="w-full">
          {testStatus === "testing" ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Testing Model...
            </>
          ) : (
            "Test Model Connection"
          )}
        </Button>

        {testStatus === "success" && (
          <Alert className="border-green-500 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{testResult}</AlertDescription>
          </Alert>
        )}

        {testStatus === "error" && (
          <Alert className="border-red-500 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{testResult}</AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
