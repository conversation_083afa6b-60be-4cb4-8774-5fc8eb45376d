import { Card, CardContent } from "@/components/ui/card"
import { Calculator, Eye, CheckCircle, TrendingUp, Sun, Wind } from "lucide-react"

const features = [
  {
    icon: Calculator,
    title: "Accurate Area Calculation",
    description:
      "Precise carpet area calculations for all rooms including balconies, utility areas, and storage spaces.",
  },
  {
    icon: Eye,
    title: "Design Flaw Detection",
    description: "Identify wasted space, awkward room shapes, and poor circulation patterns before you buy.",
  },
  {
    icon: CheckCircle,
    title: "Vastu Compliance",
    description: "Comprehensive Vastu analysis based on property orientation and room placements.",
  },
  {
    icon: TrendingUp,
    title: "Space Optimization",
    description: "Evaluate how efficiently the space is utilized and get suggestions for improvement.",
  },
  {
    icon: Sun,
    title: "Natural Light Analysis",
    description: "Assess natural light potential based on window placement and room orientation.",
  },
  {
    icon: Wind,
    title: "Ventilation Assessment",
    description: "Analyze cross-ventilation possibilities and air flow patterns throughout the property.",
  },
]

export function FeatureGrid() {
  return (
    <section className="container mx-auto px-4 py-16">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-3xl font-bold text-center mb-12">Comprehensive Floor Plan Analysis</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <feature.icon className="w-12 h-12 text-blue-600 mb-4" />
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
