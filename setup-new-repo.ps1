# PowerShell script to set up new GitHub repository for Floor Plan Analyzer
# Usage: .\setup-new-repo.ps1 <github-repo-url>

param(
    [Parameter(Mandatory=$true)]
    [string]$RepoUrl
)

Write-Host "Setting up new GitHub repository for Floor Plan Analyzer..." -ForegroundColor Green

# Get the current directory (project root)
$ProjectRoot = Get-Location
Write-Host "Project root: $ProjectRoot" -ForegroundColor Yellow

# Create a temporary directory for the new repo
$TempDir = Join-Path $env:TEMP "floor-plan-analyzer-new-repo"
if (Test-Path $TempDir) {
    Write-Host "Removing existing temp directory..." -ForegroundColor Yellow
    Remove-Item $TempDir -Recurse -Force
}

Write-Host "Creating temporary directory: $TempDir" -ForegroundColor Yellow
New-Item -ItemType Directory -Path $TempDir | Out-Null

# Initialize new git repository
Write-Host "Initializing new git repository..." -ForegroundColor Yellow
Set-Location $TempDir
git init

# Copy all files except .git directory
Write-Host "Copying project files..." -ForegroundColor Yellow
$FilesToCopy = @(
    "app",
    "components", 
    "hooks",
    "lib",
    "public",
    "styles",
    ".env.local",
    ".gitignore",
    "ADMIN_DASHBOARD_ROADMAP.md",
    "components.json",
    "next.config.mjs",
    "package.json",
    "pnpm-lock.yaml",
    "postcss.config.mjs",
    "PRD_STATUS.md",
    "README.md",
    "tailwind.config.ts",
    "tsconfig.json"
)

foreach ($item in $FilesToCopy) {
    $SourcePath = Join-Path $ProjectRoot $item
    if (Test-Path $SourcePath) {
        Write-Host "Copying: $item" -ForegroundColor Cyan
        if (Test-Path $SourcePath -PathType Container) {
            # It's a directory
            Copy-Item $SourcePath -Destination $TempDir -Recurse
        } else {
            # It's a file
            Copy-Item $SourcePath -Destination $TempDir
        }
    } else {
        Write-Host "Warning: $item not found, skipping..." -ForegroundColor Yellow
    }
}

# Create a new README with updated information
Write-Host "Creating updated README..." -ForegroundColor Yellow
$NewReadme = @"
# Floor Plan Analyzer v2

AI-Powered Floor Plan Analysis Tool built with Next.js and multiple LLM integrations.

## Overview

This is a comprehensive floor plan analysis application that uses artificial intelligence to provide detailed insights about property layouts, including:

- **Room Analysis**: Detailed breakdown of room types, sizes, and purposes
- **Vastu Compliance**: Traditional Indian architectural principles assessment
- **Design Evaluation**: Identification of potential design flaws and improvements
- **Lighting & Ventilation**: Analysis of natural light and air circulation
- **Space Optimization**: Suggestions for better space utilization
- **Accessibility**: Evaluation of accessibility features and compliance
- **Storage Solutions**: Assessment of storage options and recommendations
- **Traffic Flow**: Analysis of circulation patterns and movement efficiency
- **Functionality**: Overall functional assessment of the layout

## Features

- 🤖 **Multiple AI Models**: Integration with OpenAI, Anthropic, Google, Groq, and Mistral
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile devices
- 🎨 **Modern UI**: Built with Tailwind CSS and Shadcn/UI components
- 📊 **Detailed Reports**: Comprehensive analysis with exportable PDF reports
- 💬 **AI Chat**: Interactive chat interface for specific questions about your floor plan
- 🔄 **Real-time Analysis**: Streaming responses for immediate feedback
- 📋 **Sample Gallery**: Pre-loaded sample floor plans for testing
- 🔧 **Model Testing**: Built-in tools to test AI model connectivity

## Tech Stack

- **Frontend**: Next.js 15 with App Router
- **UI Framework**: React 19
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/UI
- **AI Integration**: Vercel AI SDK
- **Schema Validation**: Zod
- **Language**: TypeScript
- **Deployment**: Vercel

## Getting Started

### Prerequisites

- Node.js 18+ 
- pnpm (recommended) or npm
- AI API keys (at least one):
  - OpenAI API key
  - Anthropic API key  
  - Google AI API key
  - Groq API key
  - Mistral API key

### Installation

1. Clone the repository:
   \`\`\`bash
   git clone $RepoUrl
   cd floor-plan-analyzer-v2
   \`\`\`

2. Install dependencies:
   \`\`\`bash
   pnpm install
   \`\`\`

3. Set up environment variables:
   \`\`\`bash
   cp .env.example .env.local
   \`\`\`

4. Add your API keys to \`.env.local\`:
   \`\`\`env
   OPENAI_API_KEY=your_openai_key_here
   ANTHROPIC_API_KEY=your_anthropic_key_here
   GOOGLE_GENERATIVE_AI_API_KEY=your_google_key_here
   GROQ_API_KEY=your_groq_key_here
   MISTRAL_API_KEY=your_mistral_key_here
   \`\`\`

5. Run the development server:
   \`\`\`bash
   pnpm dev
   \`\`\`

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

1. **Upload a Floor Plan**: Drag and drop or select a floor plan image (JPG, PNG) or PDF
2. **Choose AI Model**: Select from available AI providers
3. **Get Analysis**: Receive comprehensive analysis covering all aspects
4. **Interactive Chat**: Ask specific questions about your floor plan
5. **Export Results**: Download analysis as PDF or share via link

## Project Structure

\`\`\`
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── analysis/          # Analysis results pages
│   ├── samples/           # Sample floor plans page
│   └── ...
├── components/            # React components
│   ├── ui/               # Shadcn/UI components
│   └── ...
├── lib/                  # Utility functions and configurations
├── hooks/                # Custom React hooks
├── public/               # Static assets
└── styles/               # Global styles
\`\`\`

## Contributing

1. Fork the repository
2. Create a feature branch: \`git checkout -b feature-name\`
3. Make your changes and commit: \`git commit -m 'Add feature'\`
4. Push to the branch: \`git push origin feature-name\`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with [v0.dev](https://v0.dev) for rapid prototyping
- UI components from [Shadcn/UI](https://ui.shadcn.com/)
- AI integration powered by [Vercel AI SDK](https://sdk.vercel.ai/)
"@

Set-Content -Path "README.md" -Value $NewReadme

# Add all files to git
Write-Host "Adding files to git..." -ForegroundColor Yellow
git add .

# Create initial commit
Write-Host "Creating initial commit..." -ForegroundColor Yellow
git commit -m "Initial commit: Floor Plan Analyzer v2

- Next.js 15 application with App Router
- Multiple AI model integrations (OpenAI, Anthropic, Google, Groq, Mistral)
- Comprehensive floor plan analysis features
- Modern UI with Tailwind CSS and Shadcn/UI
- Real-time streaming analysis
- PDF export and sharing capabilities
- Sample floor plans gallery
- AI model testing tools"

# Add remote origin
Write-Host "Adding remote origin..." -ForegroundColor Yellow
git remote add origin $RepoUrl

# Create and push to main branch
Write-Host "Pushing to GitHub..." -ForegroundColor Yellow
git branch -M main
git push -u origin main

# Create a checkpoint tag
$TagName = "v1.0.0-checkpoint"
Write-Host "Creating checkpoint tag: $TagName" -ForegroundColor Yellow
git tag -a $TagName -m "Checkpoint: Initial release of Floor Plan Analyzer v2"
git push origin $TagName

Write-Host "✅ Successfully set up new repository!" -ForegroundColor Green
Write-Host "Repository URL: $RepoUrl" -ForegroundColor Cyan
Write-Host "Checkpoint tag: $TagName" -ForegroundColor Cyan
Write-Host "Temp directory: $TempDir" -ForegroundColor Yellow
Write-Host "You can now delete the temp directory if everything looks good." -ForegroundColor Yellow

# Return to original directory
Set-Location $ProjectRoot
