"use client"

import { useEffect, useState } from "react"
import { EnhancedModelTester } from "@/components/enhanced-model-tester"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertTriangle } from "lucide-react"
import { getAvailableProviders } from "@/lib/analyze-floor-plan"
import { ApiKeySecurity } from "@/components/api-key-security"

export default function TestModelsPage() {
  const [availableProviders, setAvailableProviders] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function loadProviders() {
      try {
        setLoading(true)
        const providers = await getAvailableProviders()
        setAvailableProviders(providers)
      } catch (err) {
        console.error("Failed to load providers:", err)
        setError("Failed to load AI providers. Please check your environment variables.")
      } finally {
        setLoading(false)
      }
    }

    loadProviders()
  }, [])

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">AI Model Testing</h1>
      <p className="text-gray-600 mb-4">
        Test the connection to different AI models to ensure they are working correctly for floor plan analysis.
      </p>

      <div className="mb-8">
        <ApiKeySecurity />
      </div>

      {loading ? (
        <Card>
          <CardHeader>
            <CardTitle>
              <Skeleton className="h-8 w-48" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          </CardContent>
        </Card>
      ) : error ? (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : availableProviders.length === 0 ? (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>No AI Models Available</AlertTitle>
          <AlertDescription>
            No AI models are available. Please check your environment variables and ensure at least one AI provider API
            key is set.
          </AlertDescription>
        </Alert>
      ) : (
        <EnhancedModelTester availableProviders={availableProviders} />
      )}
    </div>
  )
}
