"use client"

import { useEffect, useState } from "react"
import { AnalysisDashboard } from "@/components/analysis-dashboard"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertTriangle, Home, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface AnalysisPageProps {
  params: Promise<{ id: string }>
}

export default function AnalysisPage({ params }: AnalysisPageProps) {
  const [id, setId] = useState<string>("")
  const [analysisData, setAnalysisData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Resolve params first
    const resolveParams = async () => {
      try {
        const resolvedParams = await params
        setId(resolvedParams.id)
      } catch (error) {
        console.error("Error resolving params:", error)
        setError("Invalid analysis ID")
        setLoading(false)
      }
    }
    resolveParams()
  }, [params])

  useEffect(() => {
    if (!id) return

    const fetchData = async () => {
      setLoading(true)
      setError(null)

      try {
        console.log("Fetching analysis data for ID:", id)

        // First, try to get analysis data from sessionStorage (for fresh analyses)
        const storedData = sessionStorage.getItem(`analysis-${id}`)
        if (storedData) {
          console.log("Found stored data:", storedData.substring(0, 200) + "...")

          const parsedData = JSON.parse(storedData)
          console.log("Parsed data structure:", {
            hasProjectInfo: !!parsedData.projectInfo,
            hasRooms: !!parsedData.rooms,
            hasOverallAnalysis: !!parsedData.overall_analysis,
            hasMetadata: !!parsedData.metadata,
            keys: Object.keys(parsedData),
          })

          // Validate that we have the required data structure
          if (!parsedData.projectInfo) {
            console.warn("Missing projectInfo, attempting to reconstruct...")

            // Try to reconstruct projectInfo from metadata or create default
            parsedData.projectInfo = {
              projectName: parsedData.metadata?.fileInfo?.name || "Sample Floor Plan",
              builderName: "Sample Builder",
              city: "Bangalore",
              superArea: "1200",
              superAreaUnit: "sqft",
              propertyType: "apartment",
              dimensionUnit: "ft",
            }
          }

          // Ensure we have rooms array
          if (!parsedData.rooms || !Array.isArray(parsedData.rooms)) {
            console.warn("Missing or invalid rooms data")
            parsedData.rooms = []
          }

          // Ensure we have overall_analysis
          if (!parsedData.overall_analysis) {
            console.warn("Missing overall_analysis data")
            parsedData.overall_analysis = {
              total_carpet_area_sqft: 0,
              total_carpet_area_sqm: 0,
              loading_percentage: null,
              vastu_compliance: { score: 0, observations: [], recommendations: [] },
              design_flaws: [],
              space_optimization: { score: 0, assessment: "", suggestions: [] },
              natural_light: { score: 0, assessment: "", recommendations: [] },
            }
          }

          setAnalysisData(parsedData)
          setLoading(false)
          return
        }

        // If not in sessionStorage, check if it's a sample analysis
        if (id.startsWith("sample-")) {
          setError("Sample analysis data not found. The analysis may have expired or failed to save properly.")
          setLoading(false)
          return
        }

        // For other analyses, you would typically fetch from a database
        setError("Analysis data not found. Please upload and analyze a floor plan first.")
        setLoading(false)
      } catch (error) {
        console.error("Error fetching analysis data:", error)
        setError(`Failed to load analysis data: ${error instanceof Error ? error.message : "Unknown error"}`)
        setLoading(false)
      }
    }

    fetchData()
  }, [id])

  const handleRetry = () => {
    setError(null)
    setLoading(true)
    // Trigger re-fetch by updating a dependency
    const fetchData = async () => {
      // Re-run the fetch logic
      window.location.reload()
    }
    fetchData()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-4">
              <Skeleton className="h-12 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-[500px] w-full rounded-lg" />
            </div>
            <div className="space-y-4">
              <Skeleton className="h-8 w-2/3" />
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Alert variant="destructive" className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Analysis Not Found</AlertTitle>
              <AlertDescription className="mt-2">{error}</AlertDescription>
            </Alert>

            <div className="text-center space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button onClick={handleRetry} variant="outline">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Retry
                </Button>
                <Link href="/samples">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Home className="w-4 h-4 mr-2" />
                    Try Sample Floor Plans
                  </Button>
                </Link>
                <Link href="/">
                  <Button variant="outline">Upload Your Own Floor Plan</Button>
                </Link>
              </div>

              <div className="text-sm text-gray-600 mt-6">
                <p>If you just completed an analysis, please try:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Refreshing the page</li>
                  <li>Going back and re-running the analysis</li>
                  <li>Checking that your browser allows sessionStorage</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return <AnalysisDashboard initialData={analysisData} />
}
