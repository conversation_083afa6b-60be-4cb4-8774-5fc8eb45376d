"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Loader2, CheckCircle } from "lucide-react"

interface StreamingAnalysisProps {
  file: File
  projectInfo: any
  onComplete: (result: any) => void
}

export function StreamingAnalysis({ file, projectInfo, onComplete }: StreamingAnalysisProps) {
  const [isStreaming, setIsStreaming] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState("")
  const [findings, setFindings] = useState<string[]>([])

  const startStreamingAnalysis = async () => {
    setIsStreaming(true)
    setProgress(0)
    setFindings([])

    try {
      // Convert file to base64
      const bytes = await file.arrayBuffer()
      const base64 = Buffer.from(bytes).toString("base64")

      const response = await fetch("/api/analyze-stream", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          image: `data:${file.type};base64,${base64}`,
          projectInfo,
        }),
      })

      if (!response.body) throw new Error("No response body")

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split("\n").filter(Boolean)

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6))
              setProgress(data.progress || 0)
              setCurrentStep(data.current_step || "")
              if (data.preliminary_findings) {
                setFindings(data.preliminary_findings)
              }
            } catch (e) {
              console.error("Error parsing streaming data:", e)
            }
          }
        }
      }

      // After streaming completes, get full analysis
      const formData = new FormData()
      formData.append("files", file)
      formData.append("projectInfo", JSON.stringify(projectInfo))
      formData.append("llmProvider", "groq")

      const { analyzeFloorPlan } = await import("@/lib/analyze-floor-plan")
      const result = await analyzeFloorPlan(formData)
      onComplete(result)
    } catch (error) {
      console.error("Streaming analysis failed:", error)
    } finally {
      setIsStreaming(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isStreaming ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <CheckCircle className="w-5 h-5 text-green-600" />
          )}
          AI Floor Plan Analysis
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isStreaming && progress === 0 && (
          <div className="text-center">
            <p className="text-gray-600 mb-4">Ready to analyze your floor plan with AI</p>
            <Button onClick={startStreamingAnalysis} className="bg-blue-600 hover:bg-blue-700">
              Start AI Analysis
            </Button>
          </div>
        )}

        {isStreaming && (
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Analysis Progress</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>

            {currentStep && (
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm">{currentStep}</span>
              </div>
            )}

            {findings.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Preliminary Findings:</h4>
                <ul className="space-y-1">
                  {findings.map((finding, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <CheckCircle className="w-3 h-3 text-green-600" />
                      {finding}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {progress === 100 && !isStreaming && (
          <div className="text-center">
            <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-2" />
            <p className="text-green-600 font-medium">Analysis Complete!</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
