"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Loader2, CheckCircle, AlertTriangle, Info, ExternalLink } from "lucide-react"
import type { LLMProvider } from "@/lib/analyze-floor-plan"

interface EnhancedModelTesterProps {
  availableProviders: Array<{
    id: LLMProvider
    name: string
    description: string
  }>
}

export function EnhancedModelTester({ availableProviders }: EnhancedModelTesterProps) {
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider | "">("")
  const [testStatus, setTestStatus] = useState<"idle" | "testing" | "success" | "error">("idle")
  const [testResult, setTestResult] = useState<string>("")
  const [debugInfo, setDebugInfo] = useState<any>(null)

  const handleTest = async () => {
    if (!selectedProvider) return

    setTestStatus("testing")
    setTestResult("")
    setDebugInfo(null)

    try {
      const response = await fetch("/api/test-model", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          provider: selectedProvider,
        }),
      })

      const data = await response.json()

      if (data.success) {
        setTestStatus("success")
        setTestResult(data.message || "Model test successful!")
        setDebugInfo(data)
      } else {
        setTestStatus("error")
        setTestResult(data.error || "Model test failed.")
        setDebugInfo(data)
      }
    } catch (error) {
      setTestStatus("error")
      setTestResult(error instanceof Error ? error.message : "An unknown error occurred")
      setDebugInfo({ error: error instanceof Error ? error.message : "Unknown error" })
    }
  }

  const handleTestEnvironment = async () => {
    try {
      const response = await fetch("/api/check-env")
      const data = await response.json()
      setDebugInfo(data)
    } catch (error) {
      console.error("Failed to check environment:", error)
    }
  }

  return (
    <div className="space-y-6">
      <Card className="w-full">
        <CardHeader>
          <CardTitle>AI Model Tester</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select AI Model to Test</label>
            <Select value={selectedProvider} onValueChange={(value) => setSelectedProvider(value as LLMProvider)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent>
                {availableProviders.map((provider) => (
                  <SelectItem key={provider.id} value={provider.id}>
                    <div>
                      <div className="font-medium">{provider.name}</div>
                      <div className="text-xs text-gray-500">{provider.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <Button onClick={handleTest} disabled={!selectedProvider || testStatus === "testing"} className="flex-1">
              {testStatus === "testing" ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing Model...
                </>
              ) : (
                "Test Model Connection"
              )}
            </Button>
            <Button onClick={handleTestEnvironment} variant="outline">
              Check Environment
            </Button>
          </div>

          {testStatus === "success" && (
            <Alert className="border-green-500 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <AlertTitle>Success</AlertTitle>
              <AlertDescription>
                <div className="space-y-2">
                  <p>{testResult}</p>
                  {debugInfo?.sample && (
                    <div className="text-sm bg-green-100 p-2 rounded">
                      <strong>Sample Response:</strong> {debugInfo.sample}
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {testStatus === "error" && (
            <Alert className="border-red-500 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                <div className="space-y-3">
                  <p>{testResult}</p>

                  {debugInfo?.setupUrl && (
                    <div className="bg-blue-50 p-3 rounded border border-blue-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Info className="h-4 w-4 text-blue-600" />
                        <strong className="text-blue-800">Setup Instructions:</strong>
                      </div>
                      <ol className="list-decimal pl-5 space-y-1 text-sm">
                        <li>
                          Visit{" "}
                          <a
                            href={debugInfo.setupUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline inline-flex items-center gap-1"
                          >
                            {debugInfo.setupUrl} <ExternalLink className="h-3 w-3" />
                          </a>
                        </li>
                        <li>Create or copy your API key</li>
                        <li>
                          Set the environment variable{" "}
                          <code className="bg-blue-100 px-1 rounded">{debugInfo.envKey}</code> in your .env.local file
                        </li>
                        <li>Restart your development server</li>
                      </ol>
                      {debugInfo.hint && (
                        <p className="text-sm text-blue-700 mt-2">
                          <strong>Note:</strong> {debugInfo.hint}
                        </p>
                      )}
                    </div>
                  )}

                  {debugInfo?.originalError && debugInfo.originalError !== testResult && (
                    <details className="text-sm">
                      <summary className="cursor-pointer text-gray-600">Technical Details</summary>
                      <pre className="bg-gray-100 p-2 rounded mt-1 text-xs overflow-auto">
                        {debugInfo.originalError}
                      </pre>
                    </details>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {debugInfo && testStatus !== "error" && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Debug Information</AlertTitle>
              <AlertDescription>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-auto">
                  {JSON.stringify(debugInfo, null, 2)}
                </pre>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>API Key Setup Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-blue-600">OpenAI (GPT Models)</h4>
                  <p className="text-xs text-gray-600 mb-1">Environment variable: OPENAI_API_KEY</p>
                  <a
                    href="https://platform.openai.com/account/api-keys"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline inline-flex items-center gap-1 text-xs"
                  >
                    Get API Key <ExternalLink className="h-3 w-3" />
                  </a>
                  <p className="text-xs text-gray-500 mt-1">Format: sk-...</p>
                </div>

                <div>
                  <h4 className="font-medium text-green-600">Google AI (Gemini)</h4>
                  <p className="text-xs text-gray-600 mb-1">Environment variable: GOOGLE_GENERATIVE_AI_API_KEY</p>
                  <a
                    href="https://aistudio.google.com/app/apikey"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-green-600 hover:underline inline-flex items-center gap-1 text-xs"
                  >
                    Get API Key <ExternalLink className="h-3 w-3" />
                  </a>
                  <p className="text-xs text-gray-500 mt-1">Format: AIza...</p>
                </div>

                <div>
                  <h4 className="font-medium text-red-600">Mistral AI</h4>
                  <p className="text-xs text-gray-600 mb-1">Environment variable: MISTRAL_API_KEY</p>
                  <a
                    href="https://console.mistral.ai/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-red-600 hover:underline inline-flex items-center gap-1 text-xs"
                  >
                    Get API Key <ExternalLink className="h-3 w-3" />
                  </a>
                  <p className="text-xs text-gray-500 mt-1">Format: ...</p>
                </div>

                <div>
                  <h4 className="font-medium text-indigo-600">DeepSeek</h4>
                  <p className="text-xs text-gray-600 mb-1">Environment variable: DEEPSEEK_API_KEY</p>
                  <a
                    href="https://platform.deepseek.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-indigo-600 hover:underline inline-flex items-center gap-1 text-xs"
                  >
                    Get API Key <ExternalLink className="h-3 w-3" />
                  </a>
                  <p className="text-xs text-gray-500 mt-1">Format: sk-...</p>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-purple-600">Anthropic (Claude)</h4>
                  <p className="text-xs text-gray-600 mb-1">Environment variable: ANTHROPIC_API_KEY</p>
                  <a
                    href="https://console.anthropic.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-purple-600 hover:underline inline-flex items-center gap-1 text-xs"
                  >
                    Get API Key <ExternalLink className="h-3 w-3" />
                  </a>
                  <p className="text-xs text-gray-500 mt-1">Format: sk-ant-...</p>
                </div>

                <div>
                  <h4 className="font-medium text-orange-600">Groq</h4>
                  <p className="text-xs text-gray-600 mb-1">Environment variable: GROQ_API_KEY</p>
                  <a
                    href="https://console.groq.com/keys"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-orange-600 hover:underline inline-flex items-center gap-1 text-xs"
                  >
                    Get API Key <ExternalLink className="h-3 w-3" />
                  </a>
                  <p className="text-xs text-gray-500 mt-1">Format: gsk_...</p>
                </div>

                <div>
                  <h4 className="font-medium text-cyan-600">Qwen (Alibaba)</h4>
                  <p className="text-xs text-gray-600 mb-1">Environment variable: QWEN_API_KEY</p>
                  <a
                    href="https://dashscope.aliyun.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-cyan-600 hover:underline inline-flex items-center gap-1 text-xs"
                  >
                    Get API Key <ExternalLink className="h-3 w-3" />
                  </a>
                  <p className="text-xs text-gray-500 mt-1">Format: sk-or-v1-...</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-600">OpenRouter</h4>
                  <p className="text-xs text-gray-600 mb-1">Environment variable: OPENROUTER_API_KEY</p>
                  <a
                    href="https://openrouter.ai/keys"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-600 hover:underline inline-flex items-center gap-1 text-xs"
                  >
                    Get API Key <ExternalLink className="h-3 w-3" />
                  </a>
                  <p className="text-xs text-gray-500 mt-1">Format: sk-or-v1-...</p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 p-3 rounded border border-yellow-200">
              <h4 className="font-medium text-yellow-800 mb-2">Important Notes:</h4>
              <ul className="list-disc pl-5 space-y-1 text-xs text-yellow-700">
                <li>Replace placeholder values in .env.local with your actual API keys</li>
                <li>Restart your development server after updating environment variables</li>
                <li>Keep your API keys secure and never commit them to version control</li>
                <li>Some models may require billing setup or have usage limits</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
