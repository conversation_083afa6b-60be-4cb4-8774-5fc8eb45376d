"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON>cle, <PERSON>ert<PERSON>riangle, RefreshCw } from "lucide-react"

export function EnvTest() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const checkEnv = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/check-env")
      const data = await response.json()
      setResult(data)
    } catch (error) {
      console.error("Error checking environment variables:", error)
      setResult({ error: "Failed to check environment variables" })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Environment Variables Check</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={checkEnv} disabled={loading}>
          {loading ? <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> : null}
          Check Environment Variables
        </Button>

        {result && (
          <div className="space-y-4 mt-4">
            {result.success ? (
              <Alert className="border-green-500 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <AlertTitle>All required environment variables are available</AlertTitle>
                <AlertDescription>
                  <div className="mt-2">
                    <strong>Available Variables:</strong>
                    <ul className="list-disc pl-5 mt-1">
                      {result.availableVariables.map((variable: string) => (
                        <li key={variable}>{variable}</li>
                      ))}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            ) : (
              <Alert className="border-red-500 bg-red-50">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <AlertTitle>Missing environment variables</AlertTitle>
                <AlertDescription>
                  <div className="mt-2">
                    <strong>Missing Variables:</strong>
                    <ul className="list-disc pl-5 mt-1">
                      {result.missingVariables.map((variable: string) => (
                        <li key={variable}>{variable}</li>
                      ))}
                    </ul>
                  </div>
                  <div className="mt-2">
                    <strong>Available Variables:</strong>
                    <ul className="list-disc pl-5 mt-1">
                      {result.availableVariables.map((variable: string) => (
                        <li key={variable}>{variable}</li>
                      ))}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
