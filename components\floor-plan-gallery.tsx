"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Home, Maximize2 } from "lucide-react"
import Image from "next/image"

const sampleFloorPlans = [
  {
    id: "plan1",
    name: "Type-B 2BHK",
    description: "2 Bedroom apartment with living/dining room and kitchen",
    imageUrl: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/kl_fl.jpg-Cmxj3NG37AGwi5QCZmJ1bBXl1Bneev.jpeg",
    superArea: "1200",
    superAreaUnit: "sqft",
    propertyType: "apartment",
    bedrooms: 2,
    bathrooms: 2,
  },
  {
    id: "plan2",
    name: "3BHK with Hall",
    description: "3 Bedroom apartment with hall area (386 sq ft) and 2 balconies",
    imageUrl:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/1490602110110.jpg-G1rArPGf6ttaDdQl1VEwfCrts4aINg.jpeg",
    superArea: "1500",
    superAreaUnit: "sqft",
    propertyType: "apartment",
    bedrooms: 3,
    bathrooms: 2,
  },
  {
    id: "plan3",
    name: "4BHK Unit-4A",
    description: "4 Bedroom apartment with living room, dining room and multiple balconies",
    imageUrl:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/JLL_Gurgaon_Ganga%20Realty%20-%20Nandaka.jpg-9vB6u30gUDTsNj1qsDTsydHu5MnCCq.jpeg",
    superArea: "3850.18",
    superAreaUnit: "sqft",
    propertyType: "apartment",
    bedrooms: 4,
    bathrooms: 3,
  },
  {
    id: "plan4",
    name: "Flat-A 1600 sq.ft",
    description: "3 Bedroom apartment with living room, kitchen/dining area and balconies",
    imageUrl:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/island-flat.jpg-GaOnAqgegC4Mq2mS6StzScjKBioATU.jpeg",
    superArea: "1600",
    superAreaUnit: "sqft",
    propertyType: "apartment",
    bedrooms: 3,
    bathrooms: 2,
  },
  {
    id: "plan5",
    name: "3BHK + Servant + Utility",
    description: "3 Bedroom apartment with servant room and utility area (2392 sqft)",
    imageUrl:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/ganga-anantam-apartment-3-bhk-2392sqft.jpg-PyghCyV4J12ZlVN7D2z4IlUaUvvw0H.jpeg",
    superArea: "2392",
    superAreaUnit: "sqft",
    propertyType: "apartment",
    bedrooms: 3,
    bathrooms: 3,
  },
  {
    id: "plan6",
    name: "Type A - 4BHK",
    description: "4 Bedroom apartment (2550 sq ft) from Larisa Realtech",
    imageUrl:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Navraj_2550.jpg-j98jtrfNjfnj8tamUeZaguDXopkFW1.jpeg",
    superArea: "2550",
    superAreaUnit: "sqft",
    propertyType: "apartment",
    bedrooms: 4,
    bathrooms: 3,
  },
  {
    id: "plan7",
    name: "3BHK-L2 (2,795 sqft)",
    description: "3 Bedroom apartment with 2 master bedrooms, servant room and patios",
    imageUrl:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/whiteland_aspen_2795_Sq_Ft.jpg-FlHNLAzkv3zOpBHCKGBPs4rmm9T7TA.jpeg",
    superArea: "2795",
    superAreaUnit: "sqft",
    propertyType: "apartment",
    bedrooms: 3,
    bathrooms: 3,
  },
  {
    id: "plan8",
    name: "4BHK Apartments",
    description: "Two 4BHK apartments side by side with detailed area breakdowns",
    imageUrl:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/navraj-the-antalyas-apartment-4-bhk-2390sqft.jpg-MALt5ahNpqUZPwogbQj2bkEtk6VoPE.jpeg",
    superArea: "2553",
    superAreaUnit: "sqft",
    propertyType: "apartment",
    bedrooms: 4,
    bathrooms: 3,
  },
]

interface FloorPlanGalleryProps {
  onSelectFloorPlan: (floorPlan: any) => void
}

export function FloorPlanGallery({ onSelectFloorPlan }: FloorPlanGalleryProps) {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const [hoveredPlan, setHoveredPlan] = useState<string | null>(null)

  const handleSelect = (plan: any) => {
    setSelectedPlan(plan.id)
    onSelectFloorPlan(plan)
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-900">Sample Floor Plans</h2>
        <p className="text-gray-600 max-w-2xl mx-auto text-sm md:text-base">
          Select a sample floor plan to analyze or upload your own floor plan.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
        {sampleFloorPlans.map((plan, index) => (
          <Card
            key={plan.id}
            className={`group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-2 ${
              selectedPlan === plan.id
                ? "ring-2 ring-blue-500 border-blue-500 shadow-lg"
                : "border-gray-200 hover:border-blue-300"
            } bg-white/90 backdrop-blur-sm animate-fade-in`}
            style={{ animationDelay: `${index * 0.1}s` }}
            onClick={() => handleSelect(plan)}
            onMouseEnter={() => setHoveredPlan(plan.id)}
            onMouseLeave={() => setHoveredPlan(null)}
            role="button"
            tabIndex={0}
            aria-label={`Select ${plan.name} floor plan for analysis`}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault()
                handleSelect(plan)
              }
            }}
          >
            <CardContent className="p-3 md:p-4">
              <div className="aspect-square relative mb-3 overflow-hidden rounded-lg bg-gray-100">
                <Image
                  src={plan.imageUrl || "/placeholder.svg"}
                  alt={`${plan.name} floor plan layout`}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                <div
                  className={`absolute top-2 right-2 transition-all duration-300 ${
                    hoveredPlan === plan.id ? "opacity-100 scale-100" : "opacity-0 scale-75"
                  }`}
                >
                  <div className="bg-white/90 backdrop-blur-sm rounded-full p-1.5">
                    <Maximize2 className="w-4 h-4 text-gray-700" />
                  </div>
                </div>
                {selectedPlan === plan.id && (
                  <div className="absolute top-2 left-2 animate-scale-in">
                    <div className="bg-blue-500 text-white rounded-full p-1.5">
                      <ArrowRight className="w-4 h-4" />
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold text-gray-900 text-sm md:text-base line-clamp-1">{plan.name}</h3>
                  <p className="text-xs md:text-sm text-gray-600 line-clamp-2 mt-1">{plan.description}</p>
                </div>

                <div className="flex flex-wrap gap-1.5">
                  <Badge variant="secondary" className="text-xs">
                    <Home className="w-3 h-3 mr-1" />
                    {plan.bedrooms}BHK
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {plan.superArea} {plan.superAreaUnit}
                  </Badge>
                </div>

                <div className="pt-2 border-t border-gray-100">
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>
                      {plan.bedrooms} Bed • {plan.bathrooms} Bath
                    </span>
                    <span className="capitalize">{plan.propertyType}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedPlan && (
        <div className="text-center animate-fade-in">
          <Button
            onClick={() => {
              const plan = sampleFloorPlans.find((p) => p.id === selectedPlan)
              if (plan) onSelectFloorPlan(plan)
            }}
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg transition-all duration-300 transform hover:scale-105"
          >
            Analyze Selected Floor Plan <ArrowRight className="ml-2 w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  )
}
