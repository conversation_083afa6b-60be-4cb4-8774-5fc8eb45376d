"use client"

import { useState, useEffect } from "react"
import { FloorPlanGallery } from "@/components/floor-plan-gallery"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Loader2, CheckCircle, Zap, Brain, AlertTriangle } from "lucide-react"
import { useRouter } from "next/navigation"
import { analyzeFloorPlan, getAvailableProviders, type LLMProvider } from "@/lib/analyze-floor-plan"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function SamplesPage() {
  const [selectedPlan, setSelectedPlan] = useState<any>(null)
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>("groq")
  const [availableProviders, setAvailableProviders] = useState<any[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [loadingProviders, setLoadingProviders] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    async function loadProviders() {
      try {
        const providers = await getAvailableProviders()
        setAvailableProviders(providers)
        if (providers.length > 0) {
          setSelectedProvider(providers[0].id)
        } else {
          setError("No AI models available. Please check your environment variables.")
        }
      } catch (error) {
        console.error("Failed to load providers:", error)
        setError("Failed to load AI providers. Please check your environment variables.")
      } finally {
        setLoadingProviders(false)
      }
    }
    loadProviders()
  }, [])

  const handleSelectFloorPlan = (plan: any) => {
    setSelectedPlan(plan)
    setAnalysisResult(null)
    setError(null)
  }

  const handleAnalyze = async () => {
    if (!selectedPlan || !selectedProvider) {
      setError("Please select both a floor plan and an AI model.")
      return
    }

    setIsAnalyzing(true)
    setError(null)

    try {
      // Convert the sample image URL to a File object for analysis
      const response = await fetch(selectedPlan.imageUrl)
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.statusText}`)
      }

      const blob = await response.blob()
      const file = new File([blob], `${selectedPlan.id}.jpg`, { type: "image/jpeg" })

      // Create project info object
      const projectInfo = {
        projectName: selectedPlan.name,
        builderName: "Sample Builder",
        city: "Bangalore",
        superArea: selectedPlan.superArea,
        superAreaUnit: selectedPlan.superAreaUnit,
        propertyType: selectedPlan.propertyType || "apartment",
        dimensionUnit: "ft",
      }

      // Create FormData with the sample image and project info
      const formData = new FormData()
      formData.append("files", file)
      formData.append("projectInfo", JSON.stringify(projectInfo))
      formData.append("llmProvider", selectedProvider)

      console.log("Starting analysis with:", {
        fileName: file.name,
        fileSize: file.size,
        projectInfo,
        provider: selectedProvider,
      })

      // Run actual AI analysis
      const result = await analyzeFloorPlan(formData)

      console.log("Analysis result:", result)

      if (result.success && result.data) {
        // Create complete analysis data with projectInfo included
        const completeAnalysisData = {
          ...result.data,
          projectInfo: result.projectInfo || projectInfo, // Ensure projectInfo is included
          sampleImageUrl: selectedPlan.imageUrl, // Add sample image URL for display
        }

        setAnalysisResult(result)

        // Generate unique analysis ID
        const analysisId = `sample-${selectedPlan.id}-${Date.now()}`

        // Store the complete analysis result in sessionStorage
        sessionStorage.setItem(`analysis-${analysisId}`, JSON.stringify(completeAnalysisData))

        console.log("Stored analysis data:", completeAnalysisData)

        // Navigate to analysis results page
        router.push(`/analysis/${analysisId}`)
      } else {
        const errorMessage = result.error || "Analysis failed - no data returned"
        setError(errorMessage)
        console.error("Analysis failed:", result)
      }
    } catch (error) {
      console.error("Analysis failed:", error)
      const errorMessage = error instanceof Error ? error.message : "Analysis failed. Please try again."
      setError(errorMessage)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const selectedProviderInfo = availableProviders.find((p) => p.id === selectedProvider)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header Section */}
        <div className="text-center space-y-4 animate-fade-in">
          <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Sample Floor Plans
          </h1>
          <p className="text-gray-600 max-w-3xl mx-auto text-sm md:text-base leading-relaxed">
            Select a sample floor plan below to see real AI analysis using our comprehensive analysis engine. These
            samples will be processed with the same advanced AI models and prompts used for uploaded floor plans.
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="max-w-2xl mx-auto">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* AI Model Selection Card */}
        <Card className="max-w-2xl mx-auto shadow-lg border-0 bg-white/80 backdrop-blur-sm animate-slide-up">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Brain className="w-5 h-5 text-purple-600" />
              Choose AI Model for Analysis
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="ai-model" className="text-sm font-medium">
                AI Model
              </Label>
              {loadingProviders ? (
                <div className="flex items-center gap-2 p-3 border rounded-lg">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Loading available AI models...</span>
                </div>
              ) : availableProviders.length > 0 ? (
                <Select value={selectedProvider} onValueChange={(value: LLMProvider) => setSelectedProvider(value)}>
                  <SelectTrigger
                    id="ai-model"
                    className="transition-all duration-200 hover:border-blue-300 focus:ring-2 focus:ring-blue-500"
                  >
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {availableProviders.map((provider) => (
                      <SelectItem key={provider.id} value={provider.id} className="cursor-pointer">
                        <div className="space-y-1">
                          <div className="font-medium">{provider.name}</div>
                          <div className="text-xs text-gray-500">{provider.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Alert>
                  <AlertDescription>
                    No AI models available. Please check your environment variables and ensure at least one AI provider
                    API key is set.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {selectedProviderInfo && (
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200 animate-fade-in">
                <div className="flex items-center gap-2 mb-1">
                  <Zap className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-blue-800">{selectedProviderInfo.name}</span>
                </div>
                <p className="text-sm text-blue-700">{selectedProviderInfo.description}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Floor Plan Gallery */}
        <div className="animate-slide-up" style={{ animationDelay: "0.2s" }}>
          <FloorPlanGallery onSelectFloorPlan={handleSelectFloorPlan} />
        </div>

        {/* Selected Plan Analysis */}
        {selectedPlan && (
          <Card className="max-w-4xl mx-auto shadow-xl border-0 bg-white/90 backdrop-blur-sm animate-scale-in">
            <CardContent className="p-6 md:p-8">
              <div className="space-y-6">
                <div className="text-center space-y-2">
                  <h2 className="text-xl md:text-2xl font-bold text-gray-900">Selected: {selectedPlan.name}</h2>
                  <p className="text-gray-600">{selectedPlan.description}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Super Area</div>
                    <div className="font-semibold text-lg">
                      {selectedPlan.superArea} {selectedPlan.superAreaUnit}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Property Type</div>
                    <div className="font-semibold text-lg capitalize">{selectedPlan.propertyType || "Apartment"}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">AI Model</div>
                    <div className="font-semibold text-lg">{selectedProviderInfo?.name || "Not Selected"}</div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    onClick={handleAnalyze}
                    disabled={isAnalyzing || !selectedProvider || availableProviders.length === 0}
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg transition-all duration-300 transform hover:scale-105 disabled:transform-none disabled:opacity-50"
                  >
                    {isAnalyzing ? (
                      <>
                        <Loader2 className="mr-2 w-5 h-5 animate-spin" />
                        Analyzing with AI...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="mr-2 w-5 h-5" />
                        Analyze with {selectedProviderInfo?.name || "AI"}
                      </>
                    )}
                  </Button>

                  {analysisResult && (
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => router.push(`/analysis/sample-${selectedPlan.id}-${Date.now()}`)}
                      className="transition-all duration-300 hover:shadow-md"
                    >
                      View Results
                    </Button>
                  )}
                </div>

                {isAnalyzing && (
                  <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200 animate-pulse">
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <Brain className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-blue-800">
                          🤖 Running comprehensive AI analysis using {selectedProviderInfo?.name}...
                        </p>
                        <p className="text-xs text-blue-700">
                          This includes room identification, Vastu analysis, design evaluation, accessibility
                          assessment, and more.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
