import { z } from "zod"

// Define the comprehensive analysis schema using Zod
export const comprehensiveAnalysisSchema = z.object({
  overallAnalysis: z.object({
    dwellingType: z.string().nullable(),
    layoutStyle: z.string().nullable(),
    generalImpression: z.string().nullable(),
    estimatedTotalAreaCategory: z.string().nullable(),
  }),
  rooms: z.array(
    z.object({
      roomId: z.string(),
      roomNameLabel: z.string().nullable(),
      primaryFunction: z.string().nullable(),
      estimatedSizeCategory: z.string().nullable(),
      dimensions: z.object({
        length: z.number().nullable(),
        width: z.number().nullable(),
        unit: z.string().nullable(),
      }),
      numDoors: z.number().int().nullable(),
      numWindows: z.number().int().nullable(),
      connections: z.array(z.string()),
      furnitureFixtures: z.array(z.string()),
      hasNaturalLight: z.boolean().nullable(),
      hasDirectExteriorAccess: z.boolean().nullable(),
      specificObservations: z.string().nullable(),
    }),
  ),
  circulation: z.object({
    mainEntryPoints: z.number().int().nullable(),
    primaryPathwaysDescription: z.string().nullable(),
    potentialBottlenecks: z.array(z.string()),
    flowEfficiency: z.string().nullable(),
  }),
  naturalLightAndVentilation: z.object({
    overallWindowDistribution: z.string().nullable(),
    roomsWithAbundantLight: z.array(z.string()),
    roomsWithLimitedLight: z.array(z.string()),
    crossVentilationPotential: z.string().nullable(),
  }),
  functionalityErgonomics: z.object({
    kitchenLayoutEfficiency: z.string().nullable(),
    privacyLevels: z.string().nullable(),
    potentialNoiseIssues: z.array(z.string()),
  }),
  storage: z.object({
    dedicatedStorageSpaces: z.array(
      z.object({
        storageType: z.string().nullable(),
        locationRoomId: z.string().nullable(),
        sizeCategory: z.string().nullable(),
      }),
    ),
    overallStorageSufficiency: z.string().nullable(),
  }),
  accessibilityFeaturesObserved: z.object({
    doorwaysAppearWideEnoughForWheelchair: z.boolean().nullable(),
    levelChangesWithinUnit: z.number().int().nullable(),
    mainBathroomManeuverabilitySpace: z.string().nullable(),
  }),
  keyFeaturesChecklist: z.object({
    isOpenPlanLivingDiningKitchen: z.boolean().nullable(),
    hasMasterSuite: z.boolean().nullable(),
    numBedrooms: z.number().int().nullable(),
    numBathroomsFull: z.number().int().nullable(),
    numBathroomsHalf: z.number().int().nullable(),
    hasDedicatedLaundry: z.boolean().nullable(),
    hasBalconyPatioDeck: z.boolean().nullable(),
    numBalconiesPatiosDecks: z.number().int().nullable(),
    hasFireplace: z.boolean().nullable(),
    hasKitchenIsland: z.boolean().nullable(),
  }),
  vastuCompliance: z.object({
    overallVastuScore: z.number().min(0).max(10).nullable(),
    mainEntranceDirection: z.string().nullable(),
    mainEntranceVastuCompliance: z.object({
      isCompliant: z.boolean().nullable(),
      issues: z.array(z.string()),
      recommendations: z.array(z.string()),
    }),
    roomPlacements: z.object({
      kitchen: z.object({
        direction: z.string().nullable(),
        isVastuCompliant: z.boolean().nullable(),
        issues: z.array(z.string()),
        recommendations: z.array(z.string()),
      }),
      masterBedroom: z.object({
        direction: z.string().nullable(),
        isVastuCompliant: z.boolean().nullable(),
        issues: z.array(z.string()),
        recommendations: z.array(z.string()),
      }),
      livingRoom: z.object({
        direction: z.string().nullable(),
        isVastuCompliant: z.boolean().nullable(),
        issues: z.array(z.string()),
        recommendations: z.array(z.string()),
      }),
      bathrooms: z.object({
        directions: z.array(z.string()),
        isVastuCompliant: z.boolean().nullable(),
        issues: z.array(z.string()),
        recommendations: z.array(z.string()),
      }),
      poojaRoom: z.object({
        present: z.boolean().nullable(),
        direction: z.string().nullable(),
        isVastuCompliant: z.boolean().nullable(),
        issues: z.array(z.string()),
        recommendations: z.array(z.string()),
      }),
    }),
    elementalBalance: z.object({
      fireElement: z.object({
        placement: z.string().nullable(),
        compliance: z.boolean().nullable(),
        notes: z.string().nullable(),
      }),
      waterElement: z.object({
        placement: z.string().nullable(),
        compliance: z.boolean().nullable(),
        notes: z.string().nullable(),
      }),
      earthElement: z.object({
        placement: z.string().nullable(),
        compliance: z.boolean().nullable(),
        notes: z.string().nullable(),
      }),
      airElement: z.object({
        placement: z.string().nullable(),
        compliance: z.boolean().nullable(),
        notes: z.string().nullable(),
      }),
      spaceElement: z.object({
        placement: z.string().nullable(),
        compliance: z.boolean().nullable(),
        notes: z.string().nullable(),
      }),
    }),
    directionAnalysis: z.object({
      northZone: z.object({
        rooms: z.array(z.string()),
        vastuSuitability: z.string().nullable(),
        recommendations: z.array(z.string()),
      }),
      eastZone: z.object({
        rooms: z.array(z.string()),
        vastuSuitability: z.string().nullable(),
        recommendations: z.array(z.string()),
      }),
      southZone: z.object({
        rooms: z.array(z.string()),
        vastuSuitability: z.string().nullable(),
        recommendations: z.array(z.string()),
      }),
      westZone: z.object({
        rooms: z.array(z.string()),
        vastuSuitability: z.string().nullable(),
        recommendations: z.array(z.string()),
      }),
      northEastZone: z.object({
        rooms: z.array(z.string()),
        vastuSuitability: z.string().nullable(),
        recommendations: z.array(z.string()),
      }),
      southEastZone: z.object({
        rooms: z.array(z.string()),
        vastuSuitability: z.string().nullable(),
        recommendations: z.array(z.string()),
      }),
      southWestZone: z.object({
        rooms: z.array(z.string()),
        vastuSuitability: z.string().nullable(),
        recommendations: z.array(z.string()),
      }),
      northWestZone: z.object({
        rooms: z.array(z.string()),
        vastuSuitability: z.string().nullable(),
        recommendations: z.array(z.string()),
      }),
    }),
    specificVastuGuidelines: z.object({
      brahmasthan: z.object({
        isClear: z.boolean().nullable(),
        issues: z.array(z.string()),
        recommendations: z.array(z.string()),
      }),
      slopes: z.object({
        floorSlope: z.string().nullable(),
        compliance: z.boolean().nullable(),
        recommendations: z.array(z.string()),
      }),
      openSpaces: z.object({
        northEastOpen: z.boolean().nullable(),
        southWestHeavy: z.boolean().nullable(),
        recommendations: z.array(z.string()),
      }),
    }),
    overallVastuAssessment: z.object({
      strengths: z.array(z.string()),
      majorIssues: z.array(z.string()),
      priorityRecommendations: z.array(z.string()),
      remedialMeasures: z.array(z.string()),
    }),
  }),
  positiveAspects: z.array(z.string()),
  potentialIssuesAndImprovements: z.array(
    z.object({
      issue: z.string(),
      suggestion: z.string().nullable(),
    }),
  ),
  imageAnalysisConfidence: z.object({
    overallClarityOfPlan: z.string().nullable(),
    completenessOfLabels: z.string().nullable(),
  }),
  unidentifiableSpaces: z.array(
    z.object({
      spaceId: z.string(),
      descriptionOrLocation: z.string().nullable(),
      possibleFunctions: z.array(z.string()),
    }),
  ),
})

export type ComprehensiveAnalysisResult = z.infer<typeof comprehensiveAnalysisSchema>
