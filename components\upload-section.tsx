"use client"

import { useState, useCallback, useEffect } from "react"
import { useDropzone } from "react-dropzone"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Upload, FileImage, Loader2, AlertTriangle, CheckCircle } from "lucide-react"
import { analyzeFloorPlan, getAvailableProviders, type LLMProvider } from "@/lib/analyze-floor-plan"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { StreamingAnalysis } from "@/components/streaming-analysis"
import Link from "next/link"

export function UploadSection() {
  const [files, setFiles] = useState<File[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [projectInfo, setProjectInfo] = useState({
    projectName: "",
    builderName: "",
    city: "",
    superArea: "",
    superAreaUnit: "sqft",
    propertyType: "",
    dimensionUnit: "ft",
  })

  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>("groq")
  const [availableProviders, setAvailableProviders] = useState<any[]>([])
  const [isLoadingProviders, setIsLoadingProviders] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [showStreamingAnalysis, setShowStreamingAnalysis] = useState(false)

  useEffect(() => {
    async function loadProviders() {
      try {
        const providers = await getAvailableProviders()
        setAvailableProviders(providers)

        if (providers.length > 0) {
          setSelectedProvider(providers[0].id)
        } else {
          setError("No AI providers available. Please check your environment variables.")
        }
      } catch (err) {
        console.error("Failed to load providers:", err)
        setError("Failed to load AI providers. Please check your environment variables.")
      } finally {
        setIsLoadingProviders(false)
      }
    }

    loadProviders()
  }, [])

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setFiles(acceptedFiles)
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
      "application/pdf": [".pdf"],
    },
    maxFiles: 5,
  })

  const handleAnalyze = async () => {
    if (files.length === 0) return

    setIsAnalyzing(true)
    setError(null)

    try {
      const formData = new FormData()
      files.forEach((file) => formData.append("files", file))
      formData.append("projectInfo", JSON.stringify(projectInfo))
      formData.append("llmProvider", selectedProvider)

      const result = await analyzeFloorPlan(formData)

      if (!result.success) {
        setError(result.error || "Analysis failed")
        return
      }

      setAnalysisResult(result)
      setShowStreamingAnalysis(false)

      // Navigate to results page or show results
      window.location.href = `/analysis/${result.data.metadata.timestamp}`
    } catch (error) {
      console.error("Analysis failed:", error)
      setError(error instanceof Error ? error.message : "Analysis failed")
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleStreamingAnalysis = () => {
    if (files.length === 0) return
    setShowStreamingAnalysis(true)
  }

  const handleStreamingComplete = (result: any) => {
    setAnalysisResult(result)
    // Navigate to results page
    window.location.href = `/analysis/${result.data.metadata.timestamp}`
  }

  if (showStreamingAnalysis && files.length > 0) {
    return <StreamingAnalysis file={files[0]} projectInfo={projectInfo} onComplete={handleStreamingComplete} />
  }

  return (
    <section className="container mx-auto px-4 py-16">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold text-center mb-8">Upload Your Floor Plan</h2>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Card className="mb-8">
          <CardContent className="p-6">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:border-gray-400"
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              {isDragActive ? (
                <p className="text-lg text-blue-600">Drop the files here...</p>
              ) : (
                <div>
                  <p className="text-lg text-gray-600 mb-2">
                    Drag & drop floor plan images or PDFs here, or click to browse
                  </p>
                  <p className="text-sm text-gray-500">Supports JPG, PNG, PDF (up to 5 files)</p>
                </div>
              )}
            </div>

            {files.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Selected Files:</h4>
                <div className="space-y-2">
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <FileImage className="w-4 h-4" />
                      <span>{file.name}</span>
                      <span className="text-gray-500">({(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold mb-4">Property Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="projectName">Project Name</Label>
                <Input
                  id="projectName"
                  value={projectInfo.projectName}
                  onChange={(e) => setProjectInfo((prev) => ({ ...prev, projectName: e.target.value }))}
                  placeholder="e.g., Prestige Lakeside"
                />
              </div>
              <div>
                <Label htmlFor="builderName">Builder Name</Label>
                <Input
                  id="builderName"
                  value={projectInfo.builderName}
                  onChange={(e) => setProjectInfo((prev) => ({ ...prev, builderName: e.target.value }))}
                  placeholder="e.g., Prestige Group"
                />
              </div>
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={projectInfo.city}
                  onChange={(e) => setProjectInfo((prev) => ({ ...prev, city: e.target.value }))}
                  placeholder="e.g., Bangalore"
                />
              </div>
              <div className="flex gap-2">
                <div className="flex-1">
                  <Label htmlFor="superArea">Super Area</Label>
                  <Input
                    id="superArea"
                    value={projectInfo.superArea}
                    onChange={(e) => setProjectInfo((prev) => ({ ...prev, superArea: e.target.value }))}
                    placeholder="e.g., 1200"
                  />
                </div>
                <div className="w-24">
                  <Label htmlFor="superAreaUnit">Unit</Label>
                  <Select
                    value={projectInfo.superAreaUnit}
                    onValueChange={(value) => setProjectInfo((prev) => ({ ...prev, superAreaUnit: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sqft">sq ft</SelectItem>
                      <SelectItem value="sqm">sq m</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="propertyType">Property Type</Label>
                <Select
                  value={projectInfo.propertyType}
                  onValueChange={(value) => setProjectInfo((prev) => ({ ...prev, propertyType: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select property type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="apartment">Apartment</SelectItem>
                    <SelectItem value="villa">Villa</SelectItem>
                    <SelectItem value="builder-floor">Builder Floor</SelectItem>
                    <SelectItem value="house">Independent House</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="dimensionUnit">Dimension Unit on Plan</Label>
                <Select
                  value={projectInfo.dimensionUnit}
                  onValueChange={(value) => setProjectInfo((prev) => ({ ...prev, dimensionUnit: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ft">Feet</SelectItem>
                    <SelectItem value="m">Meters</SelectItem>
                    <SelectItem value="mm">Millimeters</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">AI Model Selection</h3>
              <Link href="/test-models">
                <Button variant="outline" size="sm">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Test Models
                </Button>
              </Link>
            </div>
            <div>
              <Label htmlFor="llmProvider">Choose AI Model</Label>
              {isLoadingProviders ? (
                <div className="flex items-center gap-2 mt-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading available AI models...</span>
                </div>
              ) : availableProviders.length > 0 ? (
                <Select value={selectedProvider} onValueChange={(value: LLMProvider) => setSelectedProvider(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {availableProviders.map((provider) => (
                      <SelectItem key={provider.id} value={provider.id}>
                        <div>
                          <div className="font-medium">{provider.name}</div>
                          <div className="text-xs text-gray-500">{provider.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Alert variant="destructive" className="mt-2">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>No AI models available</AlertTitle>
                  <AlertDescription>
                    Please check your environment variables and ensure at least one AI provider API key is set.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="text-center space-y-4">
          <Button
            onClick={handleStreamingAnalysis}
            disabled={files.length === 0 || isAnalyzing || availableProviders.length === 0}
            size="lg"
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="mr-2 w-5 h-5 animate-spin" />
                Analyzing with Streaming...
              </>
            ) : (
              "Analyze with Real-time Updates"
            )}
          </Button>

          <Button
            onClick={handleAnalyze}
            disabled={files.length === 0 || isAnalyzing || availableProviders.length === 0}
            size="lg"
            variant="outline"
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="mr-2 w-5 h-5 animate-spin" />
                Analyzing Floor Plan...
              </>
            ) : (
              "Analyze Floor Plan (Standard)"
            )}
          </Button>
        </div>
      </div>
    </section>
  )
}
