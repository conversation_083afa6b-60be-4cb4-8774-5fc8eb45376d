import { streamObject } from "ai"
import { groq } from "@ai-sdk/groq"
import { z } from "zod"

// Simplified schema for streaming
const streamingAnalysisSchema = z.object({
  status: z.string(),
  progress: z.number().min(0).max(100),
  current_step: z.string(),
  rooms_identified: z.number().optional(),
  preliminary_findings: z.array(z.string()).optional(),
  estimated_carpet_area: z.number().optional(),
})

export async function POST(req: Request) {
  try {
    const { image, projectInfo } = await req.json()

    const result = streamObject({
      model: groq("llama-3.2-90b-vision-preview"),
      schema: streamingAnalysisSchema,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Analyze this floor plan step by step. Provide streaming updates on your progress:
              
              1. Start with status "analyzing" and progress 0
              2. Identify rooms and update progress to 25
              3. Calculate areas and update progress to 50  
              4. Assess design and update progress to 75
              5. Complete analysis and update progress to 100
              
              Property: ${projectInfo.projectName} in ${projectInfo.city}
              Super Area: ${projectInfo.superArea} ${projectInfo.superAreaUnit}`,
            },
            {
              type: "image",
              image: image,
            },
          ],
        },
      ],
    })

    return result.toTextStreamResponse()
  } catch (error) {
    return new Response(JSON.stringify({ error: "Streaming analysis failed" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}
