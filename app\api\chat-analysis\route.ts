import { streamText } from "ai"
import { groq } from "@ai-sdk/groq"

export async function POST(req: Request) {
  const { messages, analysisData } = await req.json()

  const result = streamText({
    model: groq("llama-3.1-8b-instant"),
    messages: [
      {
        role: "system",
        content: `You are an expert floor plan analyst helping homebuyers understand their property analysis. 

Here's the analysis data for the floor plan:
${JSON.stringify(analysisData, null, 2)}

Answer questions about:
- Room dimensions and areas
- Vastu compliance and recommendations  
- Design flaws and suggestions
- Space optimization
- Natural light and ventilation
- Furniture placement possibilities

Be helpful, specific, and reference the actual data from the analysis. Keep responses concise but informative.`,
      },
      ...messages,
    ],
  })

  return result.toTextStreamResponse()
}
