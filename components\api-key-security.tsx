"use client"

import { Alert, AlertDes<PERSON>, AlertTitle } from "@/components/ui/alert"
import { Shield } from "lucide-react"

export function ApiKeySecurity() {
  return (
    <Alert className="bg-green-50 border-green-200">
      <Shield className="h-4 w-4 text-green-600" />
      <AlertTitle>API Key Security</AlertTitle>
      <AlertDescription>
        <div className="space-y-2 text-sm">
          <p>
            Your API keys are securely stored in environment variables and are only used server-side. They are never:
          </p>
          <ul className="list-disc pl-5 space-y-1">
            <li>Exposed to the client-side code</li>
            <li>Logged to the console or server logs</li>
            <li>Stored in databases or persistent storage</li>
            <li>Transmitted to any third parties</li>
          </ul>
          <p className="text-xs text-green-700">
            For additional security, consider using API key rotation and setting usage limits on your API keys.
          </p>
        </div>
      </AlertDescription>
    </Alert>
  )
}
